#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json

from kafka import KafkaAdminClient, TopicPartition, OffsetAndMetadata, ConsumerRebalanceListener  # noqa
from kafka import KafkaConsumer as KafkaConsumerCore  # noqa
from kafka.admin import NewTopic  # noqa
from loguru import logger

from kafka_app.base import KafkaBase

class RebalanceListener(ConsumerRebalanceListener):
    def __init__(self, consumer):
        self.consumer = consumer

    def on_partitions_revoked(self, revoked):
        try:
            self.consumer.commit()
        except Exception as e:
            logger.exception("Failed to commit offsets during rebalance: {}".format(e))

    def on_partitions_assigned(self, assigned):
        pass

class KafkaConsumer(KafkaBase):
    def start(self, group):
        consumer = KafkaConsumerCore(
            bootstrap_servers=self.bootstrap_servers,
            auto_offset_reset="latest",
            enable_auto_commit=False,
            value_deserializer=lambda v: json.loads(v),
            api_version=(2, 3, 0),
            group_id=f"{group}",
            # request_timeout_ms=700000,
            # heartbeat_interval_ms=1000,
            max_poll_interval_ms=500000,
            max_poll_records=150,
            # session_timeout_ms=600000,
        )
        listener = RebalanceListener(consumer)
        consumer.subscribe(self.instance_topics, listener=listener)

        for message in consumer:
            if message is None:
                continue

            message_topic = message.topic
            message_value = message.value
            if message_value is None:
                continue
            message_type = message_value.get("type") or False
            message_payload = message_value.get("payload") or {}
            if not message_type:
                continue

            try:
                if (
                        f"[{message_topic}][{message_type}]"
                        not in self.configs.FUNCTION_CONSUMER_SUBSCRIBE
                ):
                    logger.error(
                        f"Không tìm thấy function config [{message_topic}][{message_type}]"
                    )
                    commit = True
                else:
                    try:
                        commit = self.configs.FUNCTION_CONSUMER_SUBSCRIBE[
                            f"[{message_topic}][{message_type}]"
                        ](message_payload)
                        logger.info(f"Result: {commit}")
                    except Exception as e:
                        logger.exception(e)
                        commit = True

                if commit or isinstance(commit, Exception):
                    topic_partition = TopicPartition(
                        topic=message.topic, partition=message.partition
                    )
                    offsets = {
                        topic_partition: OffsetAndMetadata(message.offset + 1, "")
                    }
                    consumer.commit(offsets)
                    logger.info(f"Offset: {offsets}")
            except Exception as e:
                logger.exception(e)

        return consumer
