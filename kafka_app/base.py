#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

import loguru
from kafka import KafkaAdminClient  # noqa
from kafka.admin import NewTopic, NewPartitions  # noqa


class KafkaBase(object):
    def __init__(self, url: str, configs: object):
        self.env = os.environ.get("APP_ENV")
        self.bootstrap_servers = url.split(",")
        self.instance_topics = configs.INSTANCE_TOPICS or []
        self.configs = configs or None
        self.check_topic()

    def check_topic(self):
        admin_client = KafkaAdminClient(bootstrap_servers=self.bootstrap_servers)
        broker_topics = admin_client.list_topics()

        topics = []

        # Make sure all topics that are to be used actually exist. This prevents
        # the consumer going into an infinite loop and 100% CPU usage when it
        # attempts to poll from a non-exising topic.
        # TODO: This will most probably be fixed in later versions of kafka_app-python
        for topic in self.instance_topics:
            if topic and topic not in broker_topics:
                loguru.logger.info(f"Topic '${topic}' does not exist. Exiting!")
                topics = [NewTopic(name=topic, num_partitions=12, replication_factor=1)]

        if len(topics) > 0:
            admin_client.create_topics(new_topics=topics, validate_only=False)

        for topic in self.instance_topics:
            self.increase_partitions(admin_client, topic, 12)
    def increase_partitions(self, admin_client, topic_name, num_partitions):
        topic_description = admin_client.describe_topics([topic_name])[0]
        current_partitions = len(topic_description['partitions'])
        if num_partitions > current_partitions:
            try:
                new_partitions = NewPartitions(total_count=num_partitions)
                admin_client.create_partitions(topic_partitions={topic_name: new_partitions})
                loguru.logger.info(f"Tăng số partition cho topic '{topic_name}' từ {current_partitions} lên {num_partitions}.")
            except Exception as e:
                loguru.logger.error(e)
        else:
            loguru.logger.info(f"Topic '{topic_name}' đã có {current_partitions} partitions. Không cần tăng thêm.")
