#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json

from loguru import logger

from app.config import config_app
from app.decorators import sqlalchemy_session
from app.extensions import kafka_producer, db
from app.helper import Helper
from app.inum import GetCodeType
from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
from app.libs.apis.urcard_api import UrcardAPI
from app.repositories import codex_repo, order_detail_repo


class ConsumerPOOrderCode:
    @classmethod
    @sqlalchemy_session(db)
    def running(cls, value):
        logger.info("PO Order Code Start...")
        try:
            logger.info(value)
            cls.get_codex(value)
        except Exception as e:
            logger.exception(e)
        return True

    @classmethod
    def get_codex(cls, payload):
        product = payload.get("product")
        if not product:
            return
        supplier = product["supplier"]
        behavior = ContextCodeApi.get_behavior_by_supplier(supplier)
        context = ContextCodeApi(behavior)

        codes, response_data = context.get_code(
            dict(
                product_id=supplier.get("product_id"),
                supplier_id=supplier.get("supplier_id"),
                product_supplier_id=supplier.get("id"),
                code_prefix=supplier.get("code_prefix"),
                code_length=supplier.get("code_length"),
                scheme_code=product.get("scheme_code"),
                product_code=supplier.get("product_code"),
                quantity=product.get("quantity"),
                price=supplier.get("supplier_value"),
                product_parent_id=supplier.get("product_parent_id"),
                expired=product.get('expired'),
                effective_date=product.get('effective_date'),
                order_id=product.get('order_id'),
                po_code=payload.get('po_code') or '',
                max_codes_per_call=supplier.get('max_codes_per_call'),
                retry_transaction_id=product.get("retry_transaction_id") or '',
                product_request_id=product.get('product_request_id') or '',
                po_request_id=payload.get('request_id') or '',
                meta_data=payload.get('meta_data') or None,
                request_round=payload.get('request_round') or None,
                supplier_order_id=0 #todo: cần lấy thông tin này thì mới chạy dc retry getcode
            )
        )
        #logger.info(codes)
        message = response_data.get("message") or ''
        logger.info(f'product:{supplier.get("product_id")} , quantity: {product.get("quantity")}, message: {message}')
        supplier_type = supplier.get("type")
        order = dict({
            "product_id": supplier.get("product_id"),
            "quantity": len(codes),
            "order_id": product.get("order_id"),
            "po_code": product.get("po_code"),
            "product_request_id": product.get("product_request_id") or '',
            "scheme_code": product.get("scheme_code") or '',
        })
        #nếu là realtime thì mới tiến hành thực hiện update code, còn order trả sau thì ko cần(real_time = 1,3, order trả sau qua webhook = 2)
        if supplier_type == 2:
            #vietguys
            cls.callback(payload.get("request_id"), payload.get("po_code"), order, message)
            return
        codexs = cls.save_code_to_database(
            supplier.get("product_id"),
            supplier["supplier_id"],
            supplier["whoexport"],
            codes,
            supplier.get("product_parent_id"),
            supplier.get("debt_recognition")
        )
        if len(codexs) > 0:

            codex_data = {
                "po_code": payload.get("po_code"),
                "request_id": payload.get("request_id"),
                "transaction_id": response_data.get("transaction_id"),
                "type": GetCodeType.GET_CODE_FROM_PORTAL.value,
                'codex_items': [{
                    "codex_id": code.get('id'),
                    "partner_codex_id": ""
                } for code in codexs]
            }
            cls.insert_codex_data(codex_data)
            cls.update_quantity_stock(supplier.get("product_id"), len(codexs))
        po_callback = cls.callback(payload.get("request_id"), payload.get("po_code"), order, message)
        return

    @classmethod
    def save_code_to_database(
            cls, product_id, supplier_id, code_type, codes, product_parent_id, debt_recognition
    ):
        codexs = []
        created = Helper.get_now_unix_timestamp()
        for code in codes:
            codexs.append({
                "product_id": product_id,
                "supplier_id": supplier_id,
                "codex": code.get("codex"),
                "codex_int": code.get("codex_int"),
                "serial": code.get("serial"),
                "pin": code.get("pin"),
                "expired_time": code.get("expired"),
                "code_type": code_type,
                "product_parent_id": product_parent_id,
                "debt_recognition": debt_recognition,
                "created": created
            })
        if len(codexs) > 0:
            codex_entity = codex_repo.create_many(codexs)
            return codex_entity
        return []
    @classmethod
    def update_quantity_stock(cls, product_id, quantity):
        kafka_producer.push(
            config_app.TOPIC_CHANGE_QUANTITY,
            {
                "type": "ADDITION",
                "payload": [{"quantity": quantity, "product_id": product_id}],
            },
        )
    @classmethod
    def insert_codex_data(cls, codex_data):
        kafka_producer.push(
            config_app.TOPIC_INSERT_CODEX_DATA,
            {
                "type": "INSERT_CODEX_DATA",
                "payload": codex_data
            },
        )

    @classmethod
    def callback(cls, request_id, po_code, orders, message):
        return UrcardAPI.save(**{
            "request_id": request_id,
            "po_code": po_code,
            "orders": orders,
            "successful": True if message in ['', "Success"] else False,
            "message": message,
        })
