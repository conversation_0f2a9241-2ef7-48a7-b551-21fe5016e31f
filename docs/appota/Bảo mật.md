```
<PERSON><PERSON> Mã Thẻ B<PERSON><PERSON> mật
```
```
<PERSON><PERSON><PERSON> bản: Lastest
```
# <PERSON><PERSON><PERSON> mật

## 1. Hướng dẫn tạo signature API Mua mã

## thẻ

## Ví dụ tham số truyền lên API

```
Thứ tự các tham số để tạo ra signature sẽ được sort theo thứ tự alphabet
Các trường được ký bao gồm partnerRefId + productCode + quantity
Chuỗi được tạo ra với các tham số trên sẽ là:
```
```
"partnerRefId=AB123&productCode=AC100&quantity=10"
```
```
signature = HMAC_SHA256("partnerRefId=AB123&productCode=AC100&quantity=10",
YOUR_SECRET_KEY)
```
```
Với type: "sms"
```
```
{
"partnerRefId": "AB123",
"productCode": "AC100",
"quantity": 10,
"signature": "b10294bae53e89919b3efd62a763bf3..."
}
```
```
{
"partnerRefId": "AB123",
"productCode": "VT10",
"type": "sms",
"quantity": 1,
"customerPhone": "0912345678",
"signature": "b10294bae53e89919b3efd62a763bf3..."
}
```
8/12/25, 1:41 PM Bảo mật | AppotaPay

https://docs.appotapay.com/buy-card/security 1 / 2


Các trường được ký bao gồm (^) partnerRefId + (^) productCode + (^) quantity + (^) type +
customerPhone
Tương tự thì chuỗi được tạo ra là:
"customerPhone=0912345678&partnerRefId=AB123&productCode=VT10&quantity=1&ty
pe=sms"
signature =
HMAC_SHA256("customerPhone=0912345678&partnerRefId=AB123&productCode=VT 1
0&quantity=1&type=sms", YOUR_SECRET_KEY)
8/12/25, 1:41 PM Bảo mật | AppotaPay
https://docs.appotapay.com/buy-card/security 2 / 2


