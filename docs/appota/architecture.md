# ura12 Brownfield Enhancement Architecture

## Introduction

This document outlines the architectural approach for enhancing **ura12 card provider service** với **Appota partner integration**. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development của Appota behavior module while ensuring seamless integration với existing multi-partner ecosystem.

**Relationship to Existing Architecture:**
Document này supplements existing project architecture bằng cách define how Appota behavior sẽ integrate với current Kafka-driven partner system. Where conflicts arise between new và existing patterns, document này provides guidance on maintaining consistency while implementing the enhancement theo proven vmg_v2 pattern.

### Existing Project Analysis

#### Current Project State
- **Primary Purpose**: Multi-partner card provider API service với event-driven architecture
- **Current Tech Stack**: Flask API + Kafka (messaging) + Redis (caching) + MySQL (transactional) + MongoDB (documents)  
- **Architecture Style**: Event-driven microservice với behavior pattern cho different partners
- **Deployment Method**: Docker containerized service với existing deployment pipeline

#### Available Documentation
- Appota API specifications (converted to .md format)
- PRD defining 5-story implementation sequence
- Understanding of existing vmg_v2 behavior pattern
- Configuration system patterns từ existing setup

#### Identified Constraints
- Must maintain 100% backward compatibility với existing API contracts
- Cannot modify core Kafka message flow architecture
- Must follow exact same behavior pattern như vmg_v2 implementation
- Performance impact must be minimal (< 15% memory increase)
- Security credentials must be stored trong existing secure configuration system

#### Change Log
| Change | Date | Version | Description | Author |
|--------|------|---------|-------------|--------|
| Initial Creation | 2025-08-12 | 1.0 | Initial brownfield architecture for Appota integration | Winston (Architect) |

---

## Enhancement Scope and Integration Strategy

### Enhancement Overview
**Enhancement Type**: Integration with New Systems (Appota partner)
**Scope**: Add new partner behavior to existing multi-partner ecosystem
**Integration Impact**: Minimal - isolated behavior addition following existing patterns

### Integration Approach
**Code Integration Strategy**: Single file behavior implementation (appota_behavior.py) following vmg_v2 pattern
**Database Integration**: Additive schema changes only - new appota_transactions table for tracking
**API Integration**: Maintain existing API contracts, internal routing to new behavior based on configuration
**UI Integration**: No UI changes required - purely backend integration

### Compatibility Requirements
- **Existing API Compatibility**: 100% backward compatibility with current API responses
- **Database Schema Compatibility**: Only additive changes, no modifications to existing tables
- **UI/UX Consistency**: Not applicable - backend service only
- **Performance Impact**: Maximum 15% memory increase, maintain existing response times

---

## Tech Stack Alignment

### Existing Technology Stack
| Category | Current Technology | Version | Usage in Enhancement | Notes |
|----------|-------------------|---------|---------------------|--------|
| Web Framework | Flask | Current | Host Appota behavior endpoints | No changes to Flask setup |
| Message Queue | Kafka | Current | Route messages to Appota behavior | Leverage existing message flow |
| Caching | Redis | Current | Cache JWT tokens and responses | Use existing Redis connections |
| Database (Transactional) | MySQL | Current | Store transaction mappings | Add new tables only |
| Database (Documents) | MongoDB | Current | Store raw API responses for audit | Use existing connections |

### New Technology Additions
No new technologies required - enhancement uses existing stack completely.

---

*Document continues with remaining sections...*
