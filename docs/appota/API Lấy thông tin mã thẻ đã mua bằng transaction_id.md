```
<PERSON>a Mã Thẻ API Mua mã thẻ
```
```
<PERSON><PERSON><PERSON> bản: Lastest
```
# API Mua mã thẻ

**Endpoint** : /api/v1/service/shopcard/buy

**Method** : POST

**Header** : Cách tạo JWT_TOKEN

### Tham số

```
Tham số Yêu
cầu
```
```
Kiểu dữ
liệu
Mô tả
```
```
partnerRefId √ String
```
```
Mã giao dịch phía đối tác, duy nhất cho mỗi giao
dịch
```
```
productCode √ String Mã^ sản^ phẩm^ (xem^ thêm^ phần^ Phụ^ Lục^ bảng^ mã
sản phẩm)
```
```
type String
```
```
Hình thức mua thẻ. Mặc định là "normal".
normal: Thẻ mua sẽ được trả trực tiếp.
sms: Thẻ mua sẽ được gửi trực tiếp qua tin nhắn
sms đến số điện thoại của người dùng.
```
```
customerPhone String
```
```
Là số điện thoại (định dạng: 09 x, 08 x...) sẽ sử dụng
để nhận thông tin thẻ cào gửi đến với hình thức
mua thẻ là sms
```
```
quantity √ Integer Số lượng thẻ mua
Tối đa 100 thẻ /1 lần mua với hình thức mua thẻ
```
```
{
"X-APPOTAPAY-AUTH": Bearer JWT_TOKEN,
"Content-Type": "application/json"
}
```

```
Tham số
Yêu
cầu
```
```
Kiểu dữ
liệu Mô^ tả
normal
Tối đa 1 thẻ/1 lần mua với hình thức mua thẻ sms
```
```
signature √ String
```
```
Chữ ký các tham số truyền lên API, các tham số
được đưa vào chữ ký theo thứ tự bao gồm:
partnerRefId + productCode + quantity (xem thêm
phần cách tạo signature)
```
### Dữ liệu trả về

```
Tham số Kiểu dữ liệu Mô tả
```
```
errorCode Integer Mã lỗi trả về
```
```
message String Mô tả chi tiết mã lỗi
```
```
cards
Array of
Object
```
```
Thông tin thẻ
```
```
cards[].code String Mã thẻ (ở dạng đã mã hoá)
```
```
cards[].serial String Serial thẻ
```
```
cards[].vendor String Vendor thẻ
```
```
cards[].value Integer Giá trị thẻ
```
```
cards[].expiry String
Hạn sử dụng thẻ (dang: y-m-d, vd: 29-
09-2023)
```
```
transaction Object Thông tin giao dịch
```
```
transaction.appotapayTransId String Mã giao dịch phía AppotaPay
```
```
amount Integer Số tiền giao dịch
```

```
Tham số Kiểu dữ liệu Mô tả
```
```
time String Thời gian giao dịch (dạng: d-m-Y H:i:s)
```
```
account Object Thông tin tài khoản
```
```
account.balance Integer Số dư tài khoản sau giao dịch
```
## Ví dụ

### Request

Với type: "sms"

### Response

```
{
"partnerRefId": "AB123",
"productCode": "AC100",
"quantity": 10,
"signature": "b10294bae53e89919b3efd62a763bf3..."
}
```
```
{
"partnerRefId": "AB123",
"productCode": "VT10",
"type": "sms",
"quantity": 1,
"customerPhone": "**********",
"signature": "b10294bae53e89919b3efd62a763bf3..."
}
```
```
{
"errorCode": 0,
"message": "Thành công",
"cards": [
{
"code": "b10294bae53e89919b3efd62a763bf3",
```

Với type: "sms"

```
"serial": "OTA123456789",
"vendor": "appota",
"value": 100000,
"expiry": "29-09-2023"
},
...
],
"transaction": {
"amount": 100000,
"appotapayTransId": "AP12adf21121",
"time": "10-04-2020 10:10:10",
},
"account": {
"balance": "*********"
}
}
```
```
{
"errorCode": 0,
"message": "Thành công",
"cards": [],
"transaction": {
"amount": 100000,
"appotapayTransId": "AP12adf21121",
"time": "10-04-2020 10:10:10",
},
"account": {
"balance": "*********"
}
}
```

