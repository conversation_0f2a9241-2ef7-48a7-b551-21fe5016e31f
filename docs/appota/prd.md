# ura12 Brownfield Enhancement PRD

## Intro Project Analysis and Context

### Existing Project Overview

**Analysis Source**: IDE-based fresh analysis

**Current Project State**: 
- **API Service**: Flask-based service để lấy thông tin mã thẻ từ các đối tác
- **Tech Stack**: Flask API + Redis + MySQL + MongoDB + Kafka
- **Architecture**: Event-driven với Kafka messaging (before_po_getcode → po_get_code → behavior execution)
- **Existing Behaviors**: Đã có behaviors cho partners khác (như vmg_v2)
- **New Requirement**: Thêm Appota behavior vào existing partner ecosystem

**Enhancement Type**: ✅ Integration with New Systems (Appota partner)

### Available Documentation Analysis

**Available Documentation**: ✅ Appota API Documentation (đã convert sang .md)
- API Mua mã thẻ specification
- API Lấy thông tin transaction specification  
- Bảng mã lỗi comprehensive error codes
- Bảo mật signature/authentication guide

### Enhancement Scope Definition

**Enhancement Description**: 
Thêm Appota behavior vào existing partner ecosystem để support việc lấy mã thẻ từ Appota partner thông qua existing Kafka-driven architecture.

**Impact Assessment**: ✅ Minimal Impact (isolated additions)
- New behavior module tương tự vmg_v2
- Không modify existing core architecture
- Leverage existing Kafka flow và configuration system

### Goals and Background Context

**Goals:**
- Tích hợp Appota partner vào existing multi-partner card provider system
- Maintain existing Kafka-driven flow và behavior pattern  
- Support normal mode only (direct return, max 100 cards per request)
- Implement proper error handling cho Appota-specific errors
- Ensure authentication và signature security theo Appota requirements

**Background Context:**
Service hiện tại đã có established pattern cho multi-partner integration với Kafka messaging architecture. Appota là partner mới cần được add vào ecosystem này, following existing behavior pattern như vmg_v2. Enhancement này sẽ mở rộng khả năng lấy mã thẻ từ source mới mà không impact existing functionality.

---

## Requirements

### Functional Requirements

**FR1**: Service sẽ implement AppotaBehavior class theo existing behavior pattern để integrate với Appota API cho việc lấy mã thẻ

**FR2**: System sẽ support Appota "normal" mode với khả năng mua tối đa 100 thẻ per request

**FR3**: AppotaBehavior sẽ generate HMAC_SHA256 signature với 3 parameters: partnerRefId + productCode + quantity theo alphabetical order

**FR4**: Service sẽ implement JWT token authentication với Appota API và handle token refresh khi cần

**FR5**: System sẽ transform Appota response format về existing internal card data structure để maintain compatibility với downstream services

**FR6**: Service sẽ implement comprehensive error handling cho 15+ Appota error codes với appropriate retry logic cho temporary errors (codes 35, 40, 500)

**FR7**: AppotaBehavior sẽ integrate với existing Kafka flow (before_po_getcode → po_get_code → behavior execution) mà không modify core architecture

**FR8**: System sẽ track transaction mapping giữa internal transaction ID và Appota partnerRefId/appotapayTransId

**FR9**: Service sẽ implement transaction status checking qua Appota GET API để verify transaction results

**FR10**: AppotaBehavior sẽ leverage existing configuration system để manage Appota credentials và settings

### Non-Functional Requirements

**NFR1**: Enhancement phải maintain existing performance characteristics và không exceed current memory usage hơn 15%

**NFR2**: AppotaBehavior response time không được vượt quá 5 giây cho bulk requests (100 cards)

**NFR3**: System phải support minimum 95% uptime với Appota integration, handle partner downtime gracefully

**NFR4**: All Appota API calls phải có proper logging và monitoring integration với existing system metrics

**NFR5**: Signature generation và JWT handling phải be thread-safe trong multi-threaded Flask environment

**NFR6**: System phải handle minimum 1000 concurrent Appota requests mà không impact existing partner behaviors

### Compatibility Requirements

**CR1**: Appota integration phải maintain 100% backward compatibility với existing API contracts và response formats

**CR2**: Database schema changes phải be additive only - không modify existing tables structure 

**CR3**: New Appota configuration phải integrate với existing partner config system mà không require migration

**CR4**: AppotaBehavior phải follow exact same interface pattern như vmg_v2 behavior để ensure consistency

---

## Technical Constraints and Integration Requirements

### Code Organization and Standards (Simplified)

**File Structure Approach**:
```
/behaviors/
  - appota_behavior.py  # Single file chứa all Appota logic
  - vmg_v2_behavior.py  # Existing behavior (reference)
  - base_behavior.py    # Base class (existing)
/config/
  - config.py          # Existing config file - thêm Appota section
/tests/
  - test_appota_behavior.py  # Single test file
```

**Config Integration trong existing config.py**:
```python
# Add Appota config section
APPOTA_CONFIG = {
    'base_url': 'https://api.appotapay.com',
    'api_key': os.getenv('APPOTA_API_KEY'),
    'secret_key': os.getenv('APPOTA_SECRET_KEY'), 
    'jwt_endpoint': '/api/v1/auth/token',
    'max_quantity': 100,
    'timeout': 30,
    'retry_attempts': 3,
    'retry_codes': [35, 40, 500]
}
```

---

## Epic and Story Structure

### Epic 1: Appota Partner Integration

**Epic Goal**: Integrate Appota partner vào existing multi-partner card provider ecosystem với full support cho normal mode card purchasing, maintaining 100% compatibility với existing Kafka-driven architecture và API contracts.

**Integration Requirements**: 
- Follow exact pattern của vmg_v2 behavior implementation
- Leverage existing configuration, authentication, và error handling frameworks  
- Maintain existing API response formats và Kafka message structures
- Ensure zero impact on existing partner behaviors và system performance

### Story Sequence (Risk-Minimized Approach)

#### Story 1.1: Appota Configuration Setup

As a **system administrator**,  
I want **Appota partner configuration được integrated vào existing config system**,  
so that **service có thể securely connect với Appota API endpoints với proper credentials**.

**Acceptance Criteria:**
1. **AC1**: APPOTA_CONFIG section được added vào existing config.py với all required parameters
2. **AC2**: Environment variables (APPOTA_API_KEY, APPOTA_SECRET_KEY) được properly loaded và validated
3. **AC3**: Config validation prevents service startup nếu Appota credentials missing hoặc invalid
4. **AC4**: Configuration có thể be updated at runtime without service restart
5. **AC5**: Appota config follows exact same pattern như existing VMG_V2_CONFIG structure

**Integration Verification:**
- **IV1**: Existing partner configurations remain unaffected và functional
- **IV2**: Config loading performance không degraded với additional Appota parameters  
- **IV3**: All existing config validation tests continue to pass

#### Story 1.2: Appota Behavior Foundation

As a **system developer**,
I want **AppotaBehavior class được implemented với core structure và authentication**,
so that **foundation cho Appota API integration được established theo existing behavior pattern**.

**Acceptance Criteria:**
1. **AC1**: AppotaBehavior class extends BaseBehavior với same interface như vmg_v2_behavior
2. **AC2**: JWT token authentication được implemented với proper token refresh logic
3. **AC3**: HMAC_SHA256 signature generation works correctly với 3 parameters (partnerRefId, productCode, quantity)
4. **AC4**: Basic error handling structure implemented cho authentication failures
5. **AC5**: AppotaBehavior có thể be instantiated và initialized without errors

**Integration Verification:**
- **IV1**: Existing behavior instantiation và execution unaffected
- **IV2**: Behavior registration system properly recognizes AppotaBehavior
- **IV3**: Memory usage remains within acceptable limits với new behavior class

#### Story 1.3: Appota API Buy Integration

As a **service user**,
I want **Appota buy card API được fully integrated với existing Kafka flow**,
so that **system có thể successfully purchase cards từ Appota partner theo normal mode**.

**Acceptance Criteria:**
1. **AC1**: AppotaBehavior.get_code() method successfully calls Appota buy API 
2. **AC2**: API requests include proper JWT authentication và HMAC signature
3. **AC3**: Bulk purchase support up to 100 cards per request works correctly
4. **AC4**: Appota API responses được properly parsed và transformed to internal card format
5. **AC5**: Integration works end-to-end qua existing Kafka flow (before_po_getcode → po_get_code → behavior)

**Integration Verification:**
- **IV1**: Existing Kafka message processing performance unaffected
- **IV2**: Other partner behaviors continue to function normally trong same Kafka flow
- **IV3**: System handles concurrent requests to multiple partners without conflicts

#### Story 1.4: Comprehensive Error Handling

As a **system operator**,
I want **robust error handling cho all Appota API scenarios**,
so that **system gracefully handles errors và provides proper feedback without affecting other partners**.

**Acceptance Criteria:**
1. **AC1**: All 15+ Appota error codes properly mapped to internal error system
2. **AC2**: Automatic retry logic implemented cho temporary errors (codes 35, 40, 500)
3. **AC3**: Circuit breaker pattern prevents cascade failures khi Appota unavailable
4. **AC4**: Comprehensive logging implemented cho debugging và monitoring
5. **AC5**: Error responses maintain existing API contract format

**Integration Verification:**
- **IV1**: Error scenarios không impact existing partner error handling
- **IV2**: System recovery time from errors meets existing SLA requirements
- **IV3**: Error rate monitoring và alerting work correctly

#### Story 1.5: Transaction Status and Monitoring

As a **system administrator**,
I want **complete transaction tracking và monitoring cho Appota integration**,
so that **operators có full visibility vào Appota transaction status và system health**.

**Acceptance Criteria:**
1. **AC1**: Transaction status checking via Appota GET API implemented
2. **AC2**: Transaction mapping between internal IDs và Appota transaction IDs tracked
3. **AC3**: Monitoring metrics integrated với existing dashboard system
4. **AC4**: Performance metrics (response time, success rate) properly tracked
5. **AC5**: Audit trail cho all Appota transactions maintained

**Integration Verification:**
- **IV1**: Existing monitoring systems continue to function với additional Appota metrics
- **IV2**: Database performance unaffected với additional transaction tracking
- **IV3**: Monitoring dashboard performance remains acceptable với new metrics

---

**Story Dependencies:**
- Story 1.1 → Story 1.2 (config needed for behavior initialization)
- Story 1.2 → Story 1.3 (authentication foundation needed for API calls)
- Story 1.3 → Story 1.4 (core functionality needed before error scenarios)
- Story 1.4 → Story 1.5 (error handling established before monitoring)

Each story delivers incremental value while maintaining system integrity và provides clear rollback points nếu issues arise.
