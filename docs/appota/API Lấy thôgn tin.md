```
<PERSON>a Mã Thẻ API Kiểm tra trạng thái giao dịch
Phiên bản: Lastest
```
# API Lấy danh sách mã thẻ dựa

# trên transaction_id

```
Endpoint : /api/v1/service/shopcard/transaction/{partnerRefId}
Method : GET
Header : Cách tạo JWT_TOKEN
```
## Tham số

```
Tham số Yêu cầu Kiểu dữ liệu Mô tả
partnerRefId √ String Mã giao dịch phía đối tác đưa vào API url
```
## Dữ liệu trả về

```
Tham số Kiểu dữ liệu Mô tả
errorCode Integer Mã lỗi trả về
message String Mô tả chi tiết mã lỗi
cards AOrrbajeyc^ otf Thông tin thẻ
cards[].code String Mã thẻ (ở dạng đã mã hoá)
```
```
{ "X-APPOTAPAY-AUTH": Bearer JWT_TOKEN,
```
(^) } "Content-Type": "application/json"
8/12/25, 1:40 PM API Kiểm tra trạng thái giao dịch | AppotaPay
https://docs.appotapay.com/buy-card/transaction-status 1 / 2


```
Tham số Kiểu dữ liệu Mô tả
cards[].serial String Serial thẻ
cards[].vendor String Vendor thẻ
cards[].value Integer Giá trị thẻ
cards[].expiry String H09-2023)ạn^ sử^ dụng^ thẻ^ (dang:^ y-m-d,^ vd:^ 29-
transaction Object Thông tin giao dịch
transaction.appotapayTransId String Mã giao dịch phía AppotaPay
amount Integer Số tiền giao dịch
time String Thời gian giao dịch (dạng: d-m-Y H:i:s)
```
8/12/25, 1:40 PM API Kiểm tra trạng thái giao dịch | AppotaPay

https://docs.appotapay.com/buy-card/transaction-status 2 / 2


