# Story 1.5: Transaction Status and Monitoring

## Status
Draft

## Story
**As a** system administrator,  
**I want** complete transaction tracking và monitoring cho Appota integration,  
**so that** operators có full visibility vào Appota transaction status và system health

## Acceptance Criteria

1. **AC1**: Transaction status checking via Appota GET API implemented
2. **AC2**: Transaction mapping between internal IDs và Appota transaction IDs tracked
3. **AC3**: Monitoring metrics integrated với existing dashboard system
4. **AC4**: Performance metrics (response time, success rate) properly tracked
5. **AC5**: Audit trail cho all Appota transactions maintained

## Integration Verification
- **IV1**: Existing monitoring systems continue to function với additional Appota metrics
- **IV2**: Database performance unaffected với additional transaction tracking
- **IV3**: Monitoring dashboard performance remains acceptable với new metrics

## Tasks / Subtasks

- [ ] **Task 1**: Implement transaction status checking API (AC: 1, 2)
  - [ ] Create check_transaction_status() method trong AppotaBehavior
  - [ ] Implement GET request to `/api/v1/service/shopcard/transaction/{partnerRefId}`
  - [ ] Parse transaction status response từ Appota
  - [ ] Update AppotaTransaction table với status information
  - [ ] Handle transaction not found scenarios
  - [ ] Add correlation between internal transaction IDs và Appota transaction IDs

- [ ] **Task 2**: Implement comprehensive transaction tracking (AC: 2, 5)
  - [ ] Enhance AppotaTransaction model với additional tracking fields
  - [ ] Implement transaction lifecycle tracking (pending → completed/failed)
  - [ ] Create transaction audit log entries cho all state changes
  - [ ] Store detailed transaction metadata (timestamps, amounts, card counts)
  - [ ] Implement transaction search và filtering capabilities
  - [ ] Add transaction history retention policy

- [ ] **Task 3**: Integrate monitoring metrics với existing dashboard (AC: 3, 4)
  - [ ] Create Appota-specific metrics endpoints
  - [ ] Implement performance metrics collection (response times, success rates)
  - [ ] Add Appota metrics to existing monitoring dashboard
  - [ ] Create alerting rules cho Appota-specific thresholds
  - [ ] Implement real-time metrics streaming
  - [ ] Add historical metrics aggregation

- [ ] **Task 4**: Implement performance metrics tracking (AC: 4)
  - [ ] Track API response times cho all Appota endpoints
  - [ ] Monitor success/failure rates per hour và per day
  - [ ] Implement transaction volume tracking
  - [ ] Add circuit breaker state monitoring
  - [ ] Track retry attempt frequencies
  - [ ] Monitor authentication token refresh rates

- [ ] **Task 5**: Create audit trail system (AC: 5)
  - [ ] Design audit log schema cho transaction events
  - [ ] Implement audit logging cho all transaction state changes
  - [ ] Add audit entries cho API calls, errors, retries
  - [ ] Create audit log retention và cleanup policies
  - [ ] Implement audit log search và reporting
  - [ ] Ensure audit logs comply với security requirements

- [ ] **Task 6**: Create monitoring dashboard và alerting (AC: 3, 4)
  - [ ] Create Appota-specific dashboard panels
  - [ ] Add real-time transaction status displays
  - [ ] Implement error rate monitoring với thresholds
  - [ ] Create alerting cho high error rates (>5%)
  - [ ] Add performance degradation alerts
  - [ ] Implement circuit breaker state notifications

- [ ] **Task 7**: Create comprehensive monitoring tests (All ACs, IVs)
  - [ ] Test transaction status checking với various scenarios
  - [ ] Test metrics collection và aggregation
  - [ ] Test dashboard integration và performance
  - [ ] Test alerting rules với simulated scenarios
  - [ ] Test audit trail completeness và accuracy
  - [ ] Verify no impact on existing monitoring systems
  - [ ] Load test monitoring system với high transaction volumes

## Dev Notes

### **Previous Story Dependencies**
**Required Completed Stories**:
- Story 1.1: Configuration foundation
- Story 1.2: Authentication và signature systems  
- Story 1.3: Core API integration với transaction tracking
- Story 1.4: Error handling với logging infrastructure

**Available Infrastructure từ Previous Stories**:
- Complete AppotaBehavior implementation với API integration
- Database transaction tracking trong AppotaTransaction table
- Error handling với structured logging
- Circuit breaker implementation với state tracking

### **Appota Transaction Status API**
[Source: docs/appota/API Lấy thôgn tin.md]

**Status Check Endpoint**: `GET /api/v1/service/shopcard/transaction/{partnerRefId}`

**Response Format**:
```json
{
  "errorCode": 0,
  "message": "Thành công",
  "cards": [
    {
      "code": "b10294bae53e89919b3efd62a763bf3",
      "serial": "OTA123456789",
      "vendor": "appota",
      "value": 50000,
      "expiry": "29-09-2025"
    }
  ],
  "transaction": {
    "appotapayTransId": "AP12adf21121",
    "amount": 500000,
    "time": "12-08-2025 10:10:10"
  }
}
```

### **Database Schema Enhancements**
[Source: docs/appota/architecture.md#data-models]

**AppotaTransaction Table Updates**:
- `status_last_checked`: Timestamp của last status check
- `transaction_lifecycle`: JSON field tracking state changes
- `performance_metrics`: JSON field với response times, retry counts
- `audit_trail_id`: Reference to detailed audit logs

**New Audit Log Table**:
```sql
CREATE TABLE appota_audit_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  transaction_id INT REFERENCES appota_transactions(id),
  event_type ENUM('api_call', 'state_change', 'error', 'retry'),
  event_data JSON,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_transaction_timestamp (transaction_id, timestamp)
);
```

### **Monitoring Integration Requirements**
[Source: docs/appota/architecture.md#infrastructure-deployment]

**Existing Monitoring Stack Integration**:
- Extend existing application metrics với Appota-specific counters
- Use existing monitoring dashboard framework
- Integrate với existing alerting channels
- Follow existing monitoring data retention policies

**Metrics Categories**:
1. **Business Metrics**: Transaction volume, success rates, revenue impact
2. **Performance Metrics**: Response times, throughput, error rates  
3. **System Metrics**: Circuit breaker state, retry frequencies, token refresh rates
4. **Operational Metrics**: Database performance, API availability, resource usage

### **Performance Monitoring Specifications**
[Source: docs/appota/prd.md#non-functional-requirements]

**Key Performance Indicators**:
- **Response Time**: P50, P95, P99 percentiles cho API calls
- **Success Rate**: Percentage successful transactions per hour
- **Error Rate**: Percentage failed transactions (alert threshold: >5%)
- **Transaction Volume**: Cards purchased per hour/day
- **System Health**: Circuit breaker state, retry rates

**Alerting Thresholds**:
- Error rate >5% trong 15-minute window
- Average response time >3 seconds cho 10 minutes  
- Circuit breaker open for >5 minutes
- Transaction volume drop >50% từ baseline

### **Audit Trail Requirements**
[Source: docs/appota/architecture.md#security-integration]

**Compliance Requirements**:
- Track all transaction state changes với timestamps
- Log all API interactions với request/response metadata
- Maintain audit trail cho minimum 12 months
- Ensure audit logs cannot be modified after creation
- Implement audit log integrity verification

**Audit Event Types**:
- Transaction initiated, completed, failed
- API calls với response codes và timing
- Error occurrences với error codes và context
- Retry attempts với backoff timing
- Circuit breaker state changes
- Configuration updates affecting Appota integration

### **Dashboard Integration Design**
[Source: docs/appota/architecture.md#infrastructure-deployment]

**Dashboard Components**:
- **Real-time Status Panel**: Current transaction processing status
- **Performance Graphs**: Response time trends, success rate over time
- **Error Analysis**: Error code distribution, failure patterns  
- **Volume Metrics**: Transaction volume trends, peak usage patterns
- **System Health**: Circuit breaker status, retry frequencies

**Dashboard Performance Requirements**:
- Real-time updates với <5 second refresh intervals
- Historical data queries <2 seconds response time
- Dashboard loading time <3 seconds
- Support concurrent users (10+ operators)

### **Integration Testing Strategy**
[Source: docs/appota/architecture.md#testing-strategy]

**Monitoring Test Categories**:
1. **Metrics Accuracy**: Verify metrics reflect actual system behavior
2. **Dashboard Performance**: Test dashboard responsiveness under load
3. **Alerting Reliability**: Test alert triggers với various scenarios
4. **Data Retention**: Verify proper data cleanup và archival
5. **Integration Compatibility**: Ensure existing monitoring unaffected

### Testing

**Testing Standards from Architecture:**
[Source: docs/appota/architecture.md#testing-strategy]

**Test File Locations**: 
- `/tests/integration/test_appota_monitoring.py`
- `/tests/unit/test_appota_metrics.py`

**Framework**: unittest với real database connections, mocked monitoring APIs

**Coverage Target**: 90% coverage cho monitoring và audit functionality

**Monitoring Test Categories**:
1. **Transaction Status Tests**: API calls, status updates, error scenarios
2. **Metrics Collection Tests**: Accuracy, aggregation, retention
3. **Dashboard Integration Tests**: Data display, performance, responsiveness
4. **Alerting Tests**: Threshold triggers, notification delivery  
5. **Audit Trail Tests**: Completeness, integrity, searchability
6. **Performance Tests**: High-volume scenarios, concurrent access

**Mock Requirements**:
- Mock monitoring dashboard APIs cho integration testing
- Mock alert notification systems  
- Real database connections cho transaction tracking verification
- Mock high-volume scenarios cho performance testing

**Key Test Scenarios**:
- Transaction status checking với various Appota response scenarios
- Metrics collection accuracy under normal và error conditions
- Dashboard data display với real-time updates
- Alert triggering với threshold breach scenarios
- Audit trail completeness cho various transaction lifecycles  
- Performance monitoring under high transaction volumes
- Database performance với additional monitoring queries

**Performance Testing Requirements**:
- Test monitoring overhead với 1000+ concurrent transactions
- Verify dashboard responsiveness với large data sets
- Test audit log performance với high write volumes
- Measure database impact của monitoring queries

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-12 | 1.0 | Initial story creation từ PRD và Architecture analysis | Bob (Scrum Master) |

## Dev Agent Record

*(This section will be populated by the development agent during implementation)*

### Agent Model Used
*(To be filled by dev agent)*

### Debug Log References
*(To be filled by dev agent)*

### Completion Notes List
*(To be filled by dev agent)*

### File List
*(To be filled by dev agent)*

## QA Results

*(This section will be populated by QA Agent after story completion)*
