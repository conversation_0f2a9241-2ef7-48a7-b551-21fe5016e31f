# Story 1.4: Comprehensive Error Handling

## Status
Draft

## Story
**As a** system operator,  
**I want** robust error handling cho all Appota API scenarios,  
**so that** system gracefully handles errors và provides proper feedback without affecting other partners

## Acceptance Criteria

1. **AC1**: All 15+ Appota error codes properly mapped to internal error system
2. **AC2**: Automatic retry logic implemented cho temporary errors (codes 35, 40, 500)
3. **AC3**: Circuit breaker pattern prevents cascade failures khi Appota unavailable
4. **AC4**: Comprehensive logging implemented cho debugging và monitoring
5. **AC5**: Error responses maintain existing API contract format

## Integration Verification
- **IV1**: Error scenarios không impact existing partner error handling
- **IV2**: System recovery time from errors meets existing SLA requirements
- **IV3**: Error rate monitoring và alerting work correctly

## Tasks / Subtasks

- [ ] **Task 1**: Implement Appota error code mapping system (AC: 1, 5)
  - [ ] Create APPOTA_ERROR_CODES mapping dictionary trong AppotaBehavior
  - [ ] Map all 15+ Appota error codes to internal error codes
  - [ ] Implement error message transformation cho user-friendly messages
  - [ ] Ensure error responses maintain existing API contract format
  - [ ] Add error code validation và unknown error handling

- [ ] **Task 2**: Implement retry logic cho temporary errors (AC: 2)
  - [ ] Create _should_retry_error() method để determine retry eligibility
  - [ ] Implement exponential backoff cho retry attempts (1s, 2s, 4s intervals)
  - [ ] Add maximum retry attempts configuration (default: 3)
  - [ ] Handle retry-eligible errors: codes 35, 40, 500
  - [ ] Add retry attempt logging và metrics
  - [ ] Implement retry state tracking cho individual requests

- [ ] **Task 3**: Implement circuit breaker pattern (AC: 3)
  - [ ] Create AppotaCircuitBreaker class cho failure detection
  - [ ] Track consecutive failures và success rates
  - [ ] Implement circuit states: CLOSED, OPEN, HALF_OPEN
  - [ ] Configure failure threshold (5 consecutive failures)
  - [ ] Add circuit recovery logic với timeout (60 seconds)
  - [ ] Integrate circuit breaker với get_code() method

- [ ] **Task 4**: Implement comprehensive error logging (AC: 4)
  - [ ] Add structured logging cho all error scenarios
  - [ ] Include request details, error codes, retry attempts trong logs
  - [ ] Implement error metrics collection cho monitoring
  - [ ] Add debug logging cho troubleshooting
  - [ ] Ensure sensitive data (credentials, card codes) không logged
  - [ ] Create error log format consistent với existing system

- [ ] **Task 5**: Implement error response handling (AC: 5)
  - [ ] Create standardized error response format
  - [ ] Ensure compatibility với existing API error contracts
  - [ ] Handle different error categories (authentication, business logic, system)
  - [ ] Add error correlation IDs cho tracing
  - [ ] Implement error response transformation logic
  - [ ] Test error responses với downstream consumers

- [ ] **Task 6**: Create comprehensive error handling tests (All ACs, IVs)
  - [ ] Test all 15+ Appota error code scenarios
  - [ ] Test retry logic với temporary errors
  - [ ] Test circuit breaker activation và recovery
  - [ ] Test error logging và monitoring integration
  - [ ] Test error response format compatibility
  - [ ] Verify no impact on existing partner error handling
  - [ ] Load test error scenarios cho performance verification

## Dev Notes

### **Previous Story Dependencies**
**Required Completed Stories**:
- Story 1.1: Configuration foundation
- Story 1.2: Authentication và signature foundation  
- Story 1.3: Core API integration với basic error handling

**Available Infrastructure từ Previous Stories**:
- AppotaBehavior.get_code() method với basic API calls
- Authentication và signature generation
- Database transaction tracking
- Basic API response parsing

### **Appota Error Code Specification**
[Source: docs/appota/Bảng mã lỗi.md]

**Complete Error Code Mapping**:
```python
APPOTA_ERROR_CODES = {
    0: "Success",
    1: "Thông tin yêu cầu thiếu hoặc không hợp lệ", 
    2: "Signature không hợp lệ",
    11: "Partner không tồn tại",
    12: "Số dư không đủ", 
    13: "Partner đã bị khoá",
    14: "API Key không tồn tại",
    24: "RSA Public Key chưa được thiết lập hoặc không hợp lệ",
    31: "Mã giao dịch bị trùng",
    33: "Giao dịch không thành công", 
    35: "Giao dịch đang chờ xử lý vui lòng kiểm tra lại sau",
    36: "Giao dịch không tồn tại",
    40: "Hệ thống đang bảo trì, vui lòng thử lại sau",
    71: "Thẻ không tồn tại hoặc không có sẵn",
    72: "Thẻ trong kho không đủ",
    73: "Số lượng thẻ mua truyền vào không hợp lệ",
    500: "Hệ thống gặp lỗi, vui lòng thử lại sau"
}
```

**Error Categories**:
- **Success**: Code 0
- **Permanent Failures**: Codes 1, 2, 11, 13, 14, 24, 31, 33, 36, 71, 72, 73 (no retry)
- **Temporary Errors**: Codes 35, 40, 500 (retry eligible)
- **Business Logic Errors**: Codes 12, 71, 72, 73 (return to user)

### **Retry Logic Configuration**
[Source: docs/appota/prd.md#technical-constraints, docs/appota/architecture.md#external-api-integration]

**Retry Configuration từ APPOTA_CONFIG**:
- `retry_attempts`: 3 maximum attempts
- `retry_codes`: [35, 40, 500] 
- **Backoff Strategy**: Exponential backoff (1s, 2s, 4s)
- **Total Timeout**: Maximum 30 seconds per request including retries

### **Circuit Breaker Specification**
[Source: docs/appota/architecture.md#external-api-integration]

**Circuit Breaker Parameters**:
- **Failure Threshold**: 5 consecutive failures
- **Recovery Timeout**: 60 seconds
- **Half-Open Test**: Single request to test recovery
- **Success Threshold**: 3 consecutive successes để close circuit
- **Metrics Window**: 5 minutes cho failure rate calculation

### **Error Logging Requirements**
[Source: docs/appota/architecture.md#coding-standards]

**Structured Logging Format**:
```json
{
  "timestamp": "2025-08-12T10:15:30Z",
  "level": "ERROR",
  "component": "appota_behavior", 
  "action": "buy_cards",
  "partner_ref_id": "TRANS_123",
  "appota_error_code": 35,
  "retry_attempt": 2,
  "response_time_ms": 1250,
  "correlation_id": "req_abc123"
}
```

**Security Requirements**:
- Never log API keys, secrets, JWT tokens
- Never log card codes hoặc sensitive financial data
- Log only necessary information cho debugging
- Use correlation IDs cho request tracing

### **Performance and Monitoring Requirements**
[Source: docs/appota/prd.md#non-functional-requirements]

**Error Rate Monitoring**:
- Track success/failure rates per hour
- Alert when error rate > 5% trong 15-minute window
- Monitor retry attempt frequency
- Track circuit breaker state changes

**Performance Impact**:
- Error handling shouldn't add >100ms to response time
- Retry logic must respect overall 5-second timeout
- Circuit breaker checks must be <10ms
- Logging overhead must be minimal

### **Integration with Existing Error System**
[Source: docs/appota/prd.md#compatibility-requirements]

**API Contract Compliance**:
- **CR1**: Error responses must match existing API error format
- **CR4**: Error handling must follow same patterns như vmg_v2 behavior
- **IV1**: Appota errors must not impact other partner error handling
- **IV2**: Recovery time must meet existing SLA requirements

### **Database Error Tracking**
[Source: docs/appota/architecture.md#data-models]

**AppotaTransaction Table Updates**:
- `error_code`: Store Appota error code
- `error_message`: Store error description  
- `retry_count`: Track number of retry attempts
- `circuit_breaker_state`: Track circuit breaker status
- `last_error_time`: Timestamp của last error

### Testing

**Testing Standards from Architecture:**
[Source: docs/appota/architecture.md#testing-strategy]

**Test File Location**: `/tests/unit/test_appota_error_handling.py`

**Framework**: unittest với extensive mocking cho error scenarios

**Coverage Target**: 95% coverage cho error handling code paths

**Error Testing Categories**:
1. **Error Code Mapping**: Test all 15+ error code transformations
2. **Retry Logic**: Test retry behavior với temporary errors
3. **Circuit Breaker**: Test circuit states và transitions  
4. **Logging**: Verify correct log formats và content
5. **API Compatibility**: Ensure error responses match existing contracts
6. **Performance**: Verify error handling performance impact

**Mock Requirements**:
- Mock Appota API responses cho each error code
- Mock retry timing cho faster test execution
- Mock circuit breaker state persistence
- Mock logging infrastructure cho verification
- Mock database operations cho error scenario testing

**Key Test Scenarios**:
- All individual error codes (0, 1, 2, 11, 12, 13, 14, 24, 31, 33, 35, 36, 40, 71, 72, 73, 500)
- Retry sequences cho codes 35, 40, 500
- Circuit breaker activation after 5 failures
- Circuit breaker recovery after timeout
- Error logging format verification
- API error response format compliance
- Concurrent error scenarios
- Performance impact measurement

**Load Testing Requirements**:
- Test error handling under high load (1000 concurrent errors)
- Verify circuit breaker prevents system overload
- Test retry logic performance impact
- Measure error logging overhead

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-12 | 1.0 | Initial story creation từ PRD và Architecture analysis | Bob (Scrum Master) |

## Dev Agent Record

*(This section will be populated by the development agent during implementation)*

### Agent Model Used
*(To be filled by dev agent)*

### Debug Log References
*(To be filled by dev agent)*

### Completion Notes List
*(To be filled by dev agent)*

### File List
*(To be filled by dev agent)*

## QA Results

*(This section will be populated by QA Agent after story completion)*
