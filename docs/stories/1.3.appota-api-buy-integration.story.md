# Story 1.3: Appota API Buy Integration

## Status
Draft

## Story
**As a** service user,  
**I want** Appota buy card API được fully integrated với existing Kafka flow,  
**so that** system c<PERSON> thể successfully purchase cards từ Appota partner theo normal mode

## Acceptance Criteria

1. **AC1**: AppotaBehavior.get_code() method successfully calls Appota buy API 
2. **AC2**: API requests include proper JWT authentication và HMAC signature
3. **AC3**: Bulk purchase support up to 100 cards per request works correctly
4. **AC4**: Appota API responses được properly parsed và transformed to internal card format
5. **AC5**: Integration works end-to-end qua existing Kafka flow (before_po_getcode → po_get_code → behavior)

## Integration Verification
- **IV1**: Existing Kafka message processing performance unaffected
- **IV2**: Other partner behaviors continue to function normally trong same Kafka flow
- **IV3**: System handles concurrent requests to multiple partners without conflicts

## Tasks / Subtasks

- [ ] **Task 1**: Implement core get_code() method (AC: 1, 2)
  - [ ] Complete AppotaBehavior.get_code() method implementation
  - [ ] Parse incoming Kafka message payload cho required parameters
  - [ ] Extract partnerRefId, productCode, quantity từ message payload
  - [ ] Call _get_jwt_token() và _generate_signature() từ previous story
  - [ ] Construct API request với proper authentication headers
  - [ ] Make HTTP POST request to Appota buy API endpoint

- [ ] **Task 2**: Implement Appota API buy integration (AC: 1, 3)
  - [ ] Create _call_appota_buy_api() method cho API communication
  - [ ] Support bulk purchase requests up to 100 cards per request
  - [ ] Handle normal mode purchases (no SMS functionality)
  - [ ] Implement request timeout và connection pooling
  - [ ] Add request logging cho audit trail
  - [ ] Validate API response format và status codes

- [ ] **Task 3**: Implement response parsing và transformation (AC: 4)
  - [ ] Create _handle_api_response() method cho response processing
  - [ ] Parse Appota JSON response structure (errorCode, message, cards, transaction, account)
  - [ ] Transform Appota card format to internal card data structure
  - [ ] Extract transaction information (appotapayTransId, amount, time)
  - [ ] Handle successful responses với card data extraction
  - [ ] Store raw API response trong MongoDB cho audit trail

- [ ] **Task 4**: Integrate với Kafka message flow (AC: 5)
  - [ ] Ensure get_code() method follows existing Kafka message contract
  - [ ] Process incoming messages từ po_get_code topic
  - [ ] Return responses trong expected format cho downstream processing
  - [ ] Test integration với existing Kafka consumer infrastructure
  - [ ] Verify message routing works correctly cho Appota partner
  - [ ] Add appropriate logging cho Kafka message processing

- [ ] **Task 5**: Implement database transaction tracking (Supporting functionality)
  - [ ] Create database records trong appota_transactions table
  - [ ] Store transaction mapping (internal ID ↔ Appota transaction ID)
  - [ ] Save card data trong appota_card_data table
  - [ ] Implement proper transaction rollback logic cho failures
  - [ ] Add database connection error handling

- [ ] **Task 6**: Create comprehensive integration tests (All ACs, IVs)
  - [ ] Test complete end-to-end flow từ Kafka message đến API response
  - [ ] Test bulk purchase scenarios (1, 10, 50, 100 cards)
  - [ ] Test API response parsing với real Appota response formats
  - [ ] Test Kafka integration với existing message infrastructure
  - [ ] Test concurrent request handling với multiple partners
  - [ ] Mock Appota API responses cho controlled testing
  - [ ] Verify performance impact on existing Kafka processing

## Dev Notes

### **Previous Story Dependencies**
**Required Completed Stories**: 
- Story 1.1: APPOTA_CONFIG available trong config.py
- Story 1.2: AppotaBehavior class với authentication và signature methods

**Available Methods từ Story 1.2**:
- `_get_jwt_token()`: Returns cached JWT token hoặc fetches new one
- `_generate_signature(params)`: Generates HMAC signature cho API requests  
- `AppotaBehavior.__init__()`: Initialized class với proper configuration

### **Appota API Specification**
[Source: docs/appota/API Lấy thông tin mã thẻ đã mua bằng transaction_id.md]

**Buy API Endpoint**: `POST /api/v1/service/shopcard/buy`

**Request Format**:
```json
{
  "partnerRefId": "INTERNAL_TRANS_12345",
  "productCode": "VT50", 
  "quantity": 10,
  "signature": "b10294bae53e89919b3efd62a763bf3..."
}
```

**Response Format**:
```json
{
  "errorCode": 0,
  "message": "Thành công",
  "cards": [
    {
      "code": "b10294bae53e89919b3efd62a763bf3",
      "serial": "OTA123456789",
      "vendor": "appota", 
      "value": 50000,
      "expiry": "29-09-2025"
    }
  ],
  "transaction": {
    "amount": 500000,
    "appotapayTransId": "AP12adf21121", 
    "time": "12-08-2025 10:10:10"
  },
  "account": {
    "balance": "*********"
  }
}
```

### **Kafka Message Integration**
[Source: docs/appota/architecture.md#component-architecture]
- **Message Flow**: before_po_getcode → po_get_code → AppotaBehavior.get_code()
- **Message Format**: Kafka messages contain partnerRefId, productCode, quantity
- **Response Contract**: Must return standardized card format cho downstream services
- **Partner Routing**: Messages routed to AppotaBehavior when partner='appota'

### **Data Transformation Requirements**
[Source: docs/appota/architecture.md#data-models]
- **Internal Card Format**: Transform Appota response to existing card structure
- **Transaction Tracking**: Create AppotaTransaction record với mapping
- **Card Data**: Store individual cards trong AppotaCardData table
- **Audit Trail**: Save raw API response trong MongoDB cho compliance

### **Database Schema References**
[Source: docs/appota/architecture.md#data-models]

**AppotaTransaction Table**:
- partner_ref_id, appotapay_trans_id, product_code, quantity, amount, status, error_code, raw_request, raw_response

**AppotaCardData Table**:
- transaction_id, code, serial, vendor, value, expiry

### **Performance Requirements**
[Source: docs/appota/prd.md#non-functional-requirements]
- **Response Time**: Maximum 5 seconds cho bulk requests (100 cards)
- **Concurrency**: Handle 1000 concurrent Appota requests
- **Memory Usage**: Maintain existing performance characteristics
- **Kafka Processing**: No degradation to existing message processing

### **Integration Constraints**
[Source: docs/appota/prd.md#compatibility-requirements]
- **API Compatibility**: Maintain existing API response format
- **Kafka Compatibility**: Use existing message contracts
- **Database Compatibility**: Only additive schema changes
- **Partner Isolation**: No impact on existing partner behaviors

### **Error Handling Requirements**
[Source: docs/appota/architecture.md#external-api-integration]
- **API Errors**: Handle all Appota error codes (defer comprehensive handling to Story 1.4)
- **Network Errors**: Implement timeouts và connection error handling
- **Database Errors**: Handle transaction rollback scenarios
- **Kafka Errors**: Proper error responses cho Kafka consumers

### Testing

**Testing Standards from Architecture:**
[Source: docs/appota/architecture.md#testing-strategy]

**Test File Location**: `/tests/integration/test_appota_integration.py`

**Framework**: unittest với real database connections, mocked external APIs

**Coverage Target**: 90% coverage cho integration scenarios

**Integration Test Categories**:
1. **API Integration**: End-to-end API call tests với mocked Appota responses
2. **Kafka Integration**: Message processing tests trong existing Kafka infrastructure
3. **Database Integration**: Transaction và card data storage tests
4. **Performance Tests**: Response time và concurrency tests
5. **Error Scenarios**: Network failures, API errors, database errors

**Mock Requirements**:
- Mock Appota API responses cho predictable testing
- Real database connections cho data persistence testing
- Real Kafka message processing cho integration verification
- Mock concurrent requests cho performance testing

**Key Test Scenarios**:
- Single card purchase success scenario
- Bulk purchase (100 cards) success scenario
- API response parsing với real Appota response format
- Kafka message routing với partner='appota'
- Database transaction tracking và rollback
- Concurrent request handling với multiple partners
- Performance baseline verification versus existing partners

**Performance Testing Requirements**:
- Measure response times cho 1, 10, 50, 100 card purchases
- Test concurrent requests (10, 50, 100 parallel calls)
- Memory usage measurement during bulk operations
- Kafka processing latency measurement

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-12 | 1.0 | Initial story creation từ PRD và Architecture analysis | Bob (Scrum Master) |

## Dev Agent Record

*(This section will be populated by the development agent during implementation)*

### Agent Model Used
*(To be filled by dev agent)*

### Debug Log References
*(To be filled by dev agent)*

### Completion Notes List
*(To be filled by dev agent)*

### File List
*(To be filled by dev agent)*

## QA Results

*(This section will be populated by QA Agent after story completion)*
