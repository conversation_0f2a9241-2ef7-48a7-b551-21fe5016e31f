# Story 1.2: Appota Behavior Foundation

## Status
Draft

## Story
**As a** system developer,  
**I want** AppotaBehavior class đ<PERSON><PERSON><PERSON> implemented với core structure và authentication,  
**so that** foundation cho Appota API integration đ<PERSON><PERSON><PERSON> established theo existing behavior pattern

## Acceptance Criteria

1. **AC1**: AppotaBehavior class extends BaseBehavior với same interface như vmg_v2_behavior
2. **AC2**: JWT token authentication đ<PERSON><PERSON><PERSON> implemented với proper token refresh logic
3. **AC3**: HMAC_SHA256 signature generation works correctly với 3 parameters (partnerRefId, productCode, quantity)
4. **AC4**: Basic error handling structure implemented cho authentication failures
5. **AC5**: AppotaBehavior có thể be instantiated và initialized without errors

## Integration Verification
- **IV1**: Existing behavior instantiation và execution unaffected
- **IV2**: Behavior registration system properly recognizes AppotaBehavior
- **IV3**: Memory usage remains within acceptable limits với new behavior class

## Tasks / Subtasks

- [ ] **Task 1**: Create AppotaBehavior class structure (AC: 1, 5)
  - [ ] Create `/behaviors/appota_behavior.py` file
  - [ ] Import BaseBehavior class và examine vmg_v2_behavior.py cho pattern reference
  - [ ] Implement AppotaBehavior class extending BaseBehavior
  - [ ] Override required abstract methods từ BaseBehavior interface
  - [ ] Implement `__init__` method với proper configuration loading từ APPOTA_CONFIG

- [ ] **Task 2**: Implement JWT token authentication system (AC: 2, 4)
  - [ ] Create `_get_jwt_token()` method để authenticate với Appota
  - [ ] Implement token caching trong Redis với appropriate TTL
  - [ ] Add token refresh logic khi token expires
  - [ ] Handle authentication failures với proper error responses
  - [ ] Include `X-APPOTAPAY-AUTH: Bearer {token}` trong API request headers

- [ ] **Task 3**: Implement HMAC_SHA256 signature generation (AC: 3, 4)
  - [ ] Create `_generate_signature()` method với alphabetical parameter sorting
  - [ ] Support 3-parameter signature: partnerRefId + productCode + quantity
  - [ ] Use APPOTA_SECRET_KEY từ configuration cho HMAC generation
  - [ ] Validate signature format matches Appota requirements
  - [ ] Add signature validation và error handling

- [ ] **Task 4**: Implement behavior registration và initialization (AC: 1, 5)
  - [ ] Add AppotaBehavior to behavior registry system
  - [ ] Ensure proper initialization với configuration validation
  - [ ] Test instantiation và basic method calls
  - [ ] Verify behavior can be discovered by Kafka routing system
  - [ ] Add logging cho behavior lifecycle events

- [ ] **Task 5**: Create comprehensive unit tests (All ACs, IVs)
  - [ ] Test AppotaBehavior instantiation và inheritance structure
  - [ ] Test JWT token authentication và refresh logic
  - [ ] Test HMAC signature generation với various parameter combinations
  - [ ] Test error handling scenarios (invalid config, auth failures, signature errors)
  - [ ] Test behavior registration và integration với existing system
  - [ ] Verify memory usage compliance với existing limits
  - [ ] Mock external API calls cho isolated testing

## Dev Notes

### **Previous Story Context**
**Dependency**: Story 1.1 (Appota Configuration Setup) must be completed
- Configuration foundation established trong previous story
- APPOTA_CONFIG available trong config.py với all required parameters
- Environment variables (APPOTA_API_KEY, APPOTA_SECRET_KEY) properly loaded

### **Behavior Pattern Architecture**
[Source: docs/appota/architecture.md#component-architecture]
- **Base Class**: Must extend existing `BaseBehavior` class
- **Interface Compliance**: Follow exact same method signatures như vmg_v2_behavior
- **Method Requirements**: Implement `get_code()` as main entry point (stub trong this story)
- **Registration**: Must be discoverable by existing behavior routing system

### **Authentication Implementation**
[Source: docs/appota/Bảo mật.md, docs/appota/architecture.md#external-api-integration]
- **JWT Token Endpoint**: `/api/v1/auth/token` at Appota base URL
- **Token Caching**: Store tokens trong Redis với TTL management
- **Header Format**: `X-APPOTAPAY-AUTH: Bearer {JWT_TOKEN}`
- **Refresh Logic**: Proactive token refresh trước expiration
- **Error Handling**: Handle authentication failures gracefully

### **HMAC Signature Specification**
[Source: docs/appota/Bảo mật.md]
```python
# Signature generation algorithm:
# 1. Sort parameters alphabetically: partnerRefId, productCode, quantity
# 2. Create query string: "partnerRefId=AB123&productCode=AC100&quantity=10"  
# 3. Generate HMAC: HMAC_SHA256(query_string, APPOTA_SECRET_KEY)
# 4. Return hex-encoded signature string
```

**Required Parameters for Signature**:
- `partnerRefId`: Unique transaction ID (string)
- `productCode`: Appota product code (string) 
- `quantity`: Number of cards (integer)

### **File Locations**
[Source: docs/appota/architecture.md#source-tree-integration]
- **Behavior File**: `/behaviors/appota_behavior.py` (new file)
- **Reference Pattern**: `/behaviors/vmg_v2_behavior.py` (existing - for pattern reference)
- **Base Class**: `/behaviors/base_behavior.py` (existing)
- **Test File**: `/tests/unit/test_appota_behavior.py` (new file)

### **Integration Requirements**
[Source: docs/appota/prd.md#compatibility-requirements]
- **CR1**: Must maintain 100% backward compatibility với existing API contracts
- **CR4**: Must follow exact same interface pattern như vmg_v2 behavior
- **NFR5**: All components must be thread-safe trong multi-threaded Flask environment
- **NFR3**: Handle minimum 95% uptime với graceful error handling

### **Error Handling Requirements**
[Source: docs/appota/architecture.md#external-api-integration]
- **Authentication Errors**: Log và return appropriate error responses
- **Signature Failures**: Validate signature generation và handle errors  
- **Configuration Errors**: Validate config availability và format
- **Network Errors**: Handle connection failures với appropriate timeouts

### **Performance Constraints**
[Source: docs/appota/prd.md#non-functional-requirements]
- **Memory Impact**: <15% increase từ behavior class instantiation
- **Thread Safety**: All methods must be thread-safe
- **Response Time**: Authentication operations should complete within 2 seconds
- **Caching**: Efficient token caching để minimize authentication calls

### Testing

**Testing Standards from Architecture:**
[Source: docs/appota/architecture.md#testing-strategy]

**Test File Location**: `/tests/unit/test_appota_behavior.py`

**Framework**: unittest với pytest runner, unittest.mock cho external dependencies

**Coverage Target**: 95% line coverage cho AppotaBehavior class

**Test Categories Required**:
1. **Class Structure Tests**: Inheritance, method presence, instantiation
2. **Authentication Tests**: JWT token flow, caching, refresh logic, error scenarios  
3. **Signature Tests**: HMAC generation với various parameter combinations
4. **Integration Tests**: Behavior registration, configuration loading
5. **Error Handling Tests**: Authentication failures, invalid configs, network errors
6. **Performance Tests**: Memory usage measurement, thread safety validation

**Mock Requirements**:
- Mock Redis connections cho token caching
- Mock HTTP requests cho JWT authentication
- Mock configuration loading cho error scenarios
- Mock behavior registry cho integration testing

**Key Test Scenarios**:
- Valid behavior instantiation với proper configuration
- JWT token authentication success và failure scenarios
- Token caching và refresh logic verification
- HMAC signature generation accuracy với test vectors
- Behavior registration và discovery validation
- Thread safety verification cho concurrent access
- Error handling validation cho various failure modes

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-12 | 1.0 | Initial story creation từ PRD và Architecture analysis | Bob (Scrum Master) |

## Dev Agent Record

*(This section will be populated by the development agent during implementation)*

### Agent Model Used
*(To be filled by dev agent)*

### Debug Log References
*(To be filled by dev agent)*

### Completion Notes List
*(To be filled by dev agent)*

### File List
*(To be filled by dev agent)*

## QA Results

*(This section will be populated by QA Agent after story completion)*
