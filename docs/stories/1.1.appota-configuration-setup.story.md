# Story 1.1: Appota Configuration Setup

## Status
Draft

## Story
**As a** system administrator,  
**I want** Appota partner configuration đ<PERSON><PERSON><PERSON> integrated vào existing config system,  
**so that** service có thể securely connect với Appota API endpoints với proper credentials

## Acceptance Criteria

1. **AC1**: APPOTA_CONFIG section đ<PERSON><PERSON><PERSON> added vào existing config.py với all required parameters
2. **AC2**: Environment variables (APPOTA_API_KEY, APPOTA_SECRET_KEY) đ<PERSON><PERSON><PERSON> properly loaded và validated
3. **AC3**: Config validation prevents service startup nếu Appota credentials missing hoặc invalid
4. **AC4**: Configuration có thể be updated at runtime without service restart
5. **AC5**: Appota config follows exact same pattern như existing VMG_V2_CONFIG structure

## Integration Verification
- **IV1**: Existing partner configurations remain unaffected và functional
- **IV2**: Config loading performance không degraded với additional Appota parameters  
- **IV3**: All existing config validation tests continue to pass

## Tasks / Subtasks

- [ ] **Task 1**: Add APPOTA_CONFIG section to existing config.py (AC: 1, 5)
  - [ ] Review existing VMG_V2_CONFIG structure trong config.py
  - [ ] Create APPOTA_CONFIG dictionary với same pattern như VMG_V2_CONFIG
  - [ ] Include all required parameters: base_url, api_key, secret_key, jwt_endpoint, max_quantity, timeout, retry_attempts, retry_codes
  - [ ] Ensure config section follows existing naming conventions và formatting

- [ ] **Task 2**: Implement environment variable loading và validation (AC: 2, 3)
  - [ ] Add APPOTA_API_KEY và APPOTA_SECRET_KEY environment variable loading
  - [ ] Implement validation cho required environment variables
  - [ ] Add startup validation để prevent service startup without proper credentials
  - [ ] Follow existing environment variable validation patterns

- [ ] **Task 3**: Implement runtime configuration update capability (AC: 4)
  - [ ] Research existing runtime config update mechanisms
  - [ ] Implement config reload functionality cho APPOTA_CONFIG section
  - [ ] Test configuration updates without service restart
  - [ ] Ensure thread safety cho config updates

- [ ] **Task 4**: Create comprehensive unit tests (Integration Verification)
  - [ ] Test APPOTA_CONFIG loading với valid credentials
  - [ ] Test validation failure scenarios với missing/invalid credentials
  - [ ] Test runtime configuration updates
  - [ ] Verify existing config functionality remains unchanged
  - [ ] Test performance impact của additional configuration parameters

- [ ] **Task 5**: Integration verification testing (IV: 1, 2, 3)
  - [ ] Run existing partner configuration tests để ensure no regression
  - [ ] Measure config loading performance impact
  - [ ] Verify all existing config validation tests continue to pass
  - [ ] Test concurrent access to config trong multi-threaded environment

## Dev Notes

### **Previous Story Insights**
- No previous story exists - this is the first story in the epic
- This story establishes foundation cho all subsequent Appota integration stories

### **Configuration Architecture Context**
[Source: docs/appota/architecture.md#tech-stack-alignment]
- **Existing Tech Stack**: Flask API + Redis + MySQL + MongoDB + Kafka
- **Configuration Pattern**: Centralized configuration trong config.py với environment variable injection
- **Security Requirements**: Credentials must be stored trong existing secure configuration system
- **Performance Constraint**: Maximum 15% memory increase, maintain existing response times

### **Required Configuration Parameters**
[Source: docs/appota/prd.md#technical-constraints]
```python
APPOTA_CONFIG = {
    'base_url': 'https://api.appotapay.com',
    'api_key': os.getenv('APPOTA_API_KEY'),
    'secret_key': os.getenv('APPOTA_SECRET_KEY'), 
    'jwt_endpoint': '/api/v1/auth/token',
    'max_quantity': 100,
    'timeout': 30,
    'retry_attempts': 3,
    'retry_codes': [35, 40, 500]
}
```

### **File Locations**
[Source: docs/appota/architecture.md#source-tree-integration]
- **Configuration File**: `/config/config.py` (existing file - modification required)
- **Environment Config**: `/config/environment/` (dev.py, staging.py, prod.py) - no changes required
- **Test File**: `/tests/unit/test_config.py` (extend existing hoặc create new test_appota_config.py)

### **Integration Requirements**
[Source: docs/appota/prd.md#compatibility-requirements]
- **CR3**: New Appota configuration phải integrate với existing partner config system mà không require migration
- **CR4**: Must follow exact same interface pattern như existing partner configs
- **Backward Compatibility**: 100% compatibility với existing API contracts và response formats

### **Security Constraints**  
[Source: docs/appota/architecture.md#security-integration]
- **Credential Handling**: Never log credentials hoặc sensitive data
- **Input Validation**: Validate all configuration parameters trước use
- **Environment Variables**: Use existing secure environment variable patterns
- **Access Control**: Follow existing configuration access control patterns

### **Performance Requirements**
[Source: docs/appota/prd.md#non-functional-requirements]
- **NFR1**: Enhancement phải maintain existing performance characteristics
- **NFR5**: Configuration loading phải be thread-safe trong multi-threaded Flask environment
- **Memory Impact**: Configuration additions không được exceed <15% memory increase

### Testing

**Testing Standards from Architecture:**
[Source: docs/appota/architecture.md#testing-strategy]

**Test File Location**: `/tests/unit/test_appota_config.py`

**Test Standards**: 
- **Framework**: unittest với pytest runner (consistent với existing setup)
- **Coverage Target**: 95% line coverage cho configuration-related code
- **Mock Patterns**: Use unittest.mock cho environment variable testing

**Testing Requirements cho this Story**:
- **Unit Tests**: Test configuration loading, validation, và runtime updates
- **Integration Tests**: Verify existing partner configs remain functional 
- **Performance Tests**: Measure configuration loading performance impact
- **Security Tests**: Verify credential handling và validation logic

**Test Scenarios Required**:
- Valid configuration loading với proper environment variables
- Configuration validation failure scenarios
- Runtime configuration update functionality  
- Existing configuration backward compatibility
- Multi-threaded configuration access safety
- Performance impact measurement versus baseline

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-12 | 1.0 | Initial story creation từ PRD và Architecture analysis | Bob (Scrum Master) |

## Dev Agent Record

*(This section will be populated by the development agent during implementation)*

### Agent Model Used
*(To be filled by dev agent)*

### Debug Log References
*(To be filled by dev agent)*

### Completion Notes List
*(To be filled by dev agent)*

### File List
*(To be filled by dev agent)*

## QA Results

*(This section will be populated by QA Agent after story completion)*
