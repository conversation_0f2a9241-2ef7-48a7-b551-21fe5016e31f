# coding: utf8

from Crypto import Random
from Crypto.Cipher import DES3, AES
from Crypto.Util.Padding import pad, unpad
from Crypto.Hash import SHA256, SHA1
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from Crypto.Cipher import PKCS1_v1_5 as Cipher_PKCS1_v1_5
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import padding, ec
from OpenSSL import crypto
from base64 import b64encode, b64decode
from loguru import logger

#for VietGuys
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives import padding as padding_v2
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

#forSPF
import hmac


import OpenSSL
import hashlib
import re

from app.config import config_app
from app.libs.base.utility import Utility


class UrboxTripleDes:
    @staticmethod
    def encode(data, key=config_app.URBOX_SECRET):
        md5_key = hashlib.md5(key.encode("utf-8")).hexdigest()[0:24]
        data_bytes = bytes(data.encode("utf-8"))
        cipher_txt = (b64encode(data_bytes)).decode("utf-8").replace("=", "")
        cipher = DES3.new(md5_key, DES3.MODE_ECB)
        plain_txt = cipher.encrypt(pad(cipher_txt.encode("utf-8"), DES3.block_size))

        try:
            cipher_base64 = b64encode(plain_txt, altchars="-_".encode("utf-8")).decode("utf-8")
        except Exception as e:
            logger.exception(e)
            cipher_base64 = b64encode(plain_txt).decode("utf-8")
            cipher_base64 = cipher_base64.replace("/", "_").replace("+", "-")

        txt = cipher_base64.replace("=", "")
        return txt

    @staticmethod
    def decode(data, key=config_app.URBOX_SECRET, is_base64=True):
        md5_key = hashlib.md5(key.encode("utf-8")).hexdigest()[0:24]
        spacing = "=" * (24 - len(data))
        data = "{data}{spacing}".format(data=data, spacing=spacing)

        try:
            cipher_txt = b64decode(data, altchars="-_")
        except Exception as e:
            logger.exception(e)
            data = data.replace("-", "+").replace("_", "/")
            cipher_txt = b64decode(data)

        cipher = DES3.new(md5_key, DES3.MODE_ECB)
        cipher_decrypt = cipher.decrypt(cipher_txt)
        cipher_unpad = unpad(cipher_decrypt, DES3.block_size)
        cipher_unpad_txt = cipher_unpad.decode("utf-8")
        if is_base64 is True:
            cipher_unpad_txt = "{txt}=".format(txt=cipher_unpad_txt)
            bytes_cipher_unpad_txt = bytes(cipher_unpad_txt.encode("utf-8"))
            txt = b64decode(bytes_cipher_unpad_txt + b"=" * (-len(bytes_cipher_unpad_txt) % 4)).decode("utf-8")
        else:
            txt = cipher_unpad_txt
        return txt


class VmgTripleDes:
    @staticmethod
    def decode(data, key, iv):
        cipher = DES3.new(key.encode("utf-8"), DES3.MODE_CBC, iv.encode("utf-8"))
        content = b64decode(data)
        cipher_decrypt = cipher.decrypt(content)
        try:
            plain_text = cipher_decrypt.decode("utf-8")
        except UnicodeDecodeError:
            plain_text = cipher_decrypt
        plain_text = re.sub("[^A-Za-z0-9]+", "", plain_text)
        return plain_text


class UrboxOpenSsl:
    @staticmethod
    def generate_signature(private_key, data, is_sha1=False):
        sha_type = "sha1" if is_sha1 else "sha256"
        pkey = crypto.load_privatekey(crypto.FILETYPE_PEM, private_key)
        signature = OpenSSL.crypto.sign(pkey, data, sha_type)
        return b64encode(signature).decode("utf-8")

    @staticmethod
    def verify_signature(public_key, data, signature, is_sha1=False):
        sha_type = "sha1" if is_sha1 else "sha256"
        signature = b64decode(signature)
        pkey = crypto.load_publickey(crypto.FILETYPE_PEM, public_key)
        x509 = crypto.X509()
        x509.set_pubkey(pkey)
        try:
            OpenSSL.crypto.verify(x509, signature, data, sha_type)
            return True
        except Exception as e:
            logger.exception(e)
            return False

    @staticmethod
    def rsa_decrypt(private_pem, cipher_text):
        private_key = serialization.load_pem_private_key(private_pem, None)
        plain_text = private_key.decrypt(b64decode(cipher_text), padding.PKCS1v15())
        return plain_text.decode()


class UrBoxRsa:
    @staticmethod
    def sha256_sign(signature_data, private_key, is_file=False):
        private_key = Utility.get_key(private_key, is_file)
        rsa_key = RSA.import_key(private_key)
        hash_message = SHA256.new(signature_data.encode("utf-8"))
        signature = PKCS1_v1_5.new(rsa_key).sign(hash_message)
        return b64encode(signature).decode("utf-8")

    @staticmethod
    def sha256_verify(signature, raw_string, public_key, is_file=False):
        public_key = Utility.get_key(public_key, is_file)
        rsa_key = RSA.import_key(public_key)
        verifier = PKCS1_v1_5.new(rsa_key)
        hash = SHA256.new(raw_string.encode("utf-8"))
        try:
            return verifier.verify(hash, b64decode(signature))
        except Exception as e:
            logger.exception(e)
            return False

    @staticmethod
    def sha1_sign(signature_data, private_key, is_file=False):
        private_key = Utility.get_key(private_key, is_file)
        rsa_key = RSA.import_key(private_key)
        hash_message = SHA1.new(signature_data.encode("utf-8"))
        signature = PKCS1_v1_5.new(rsa_key).sign(hash_message)
        return b64encode(signature).decode("utf-8")

    @staticmethod
    def sha1_verify(signature, raw_string, public_key, is_file=False):
        public_key = Utility.get_key(public_key, is_file)
        rsa_key = RSA.import_key(public_key)
        verifier = PKCS1_v1_5.new(rsa_key)
        hash = SHA1.new(raw_string.encode("utf-8"))
        try:
            verifier.verify(hash, b64decode(signature))
            return True
        except Exception as e:
            return False

    @staticmethod
    def sha1_decode(ciphertext, private_key, is_file=False):
        private_key = Utility.get_key(private_key, is_file)
        rsa_key = RSA.import_key(private_key)
        b64_encode_ciphertext = UrBoxCrypt.base64_decode(ciphertext)
        digest_size = SHA1.new(b64_encode_ciphertext).digest_size
        sentinel = Random.new().read(digest_size)
        raw_string = Cipher_PKCS1_v1_5.new(rsa_key).decrypt(
            b64_encode_ciphertext, sentinel
        )
        return raw_string.decode("utf-8")


class UrBoxHash:
    @staticmethod
    def sha256(string):
        return hashlib.sha256(string.encode("utf8")).hexdigest()

    @staticmethod
    def md5(string):
        return hashlib.md5(string)


class UrBoxCrypt:
    @staticmethod
    def base64_encode(string):
        return b64encode(string.encode("utf-8")).decode("utf-8")

    @staticmethod
    def base64_decode(string):
        return b64decode(string)


class ShopeeAESCipher(object):

    def __init__(self, key):
        self.bs = AES.block_size
        self.key = hashlib.sha256(key.encode()).digest()

    @staticmethod
    def encrypt(string, key):
        raw = ShopeeAESCipher.pad(string)
        key = hashlib.sha256(key.encode()).digest()
        iv = Random.new().read(AES.block_size)
        cipher = AES.new(key, AES.MODE_CBC, iv)
        return b64encode(iv + cipher.encrypt(raw.encode())).decode('utf-8')

    @staticmethod
    def decrypt(string, key):
        enc = b64decode(string)
        key = hashlib.sha256(key.encode()).digest()
        iv = enc[:AES.block_size]
        cipher = AES.new(key, AES.MODE_CBC, iv)
        return ShopeeAESCipher.unpad(cipher.decrypt(enc[AES.block_size:])).decode('utf-8')

    @staticmethod
    def pad(s):
        return s + (AES.block_size - len(s) % AES.block_size) * chr(AES.block_size - len(s) % AES.block_size)

    @staticmethod
    def unpad(s):
        return s[:-ord(s[len(s) - 1:])]


class VietGuysCipher:

    @staticmethod
    def share_key(private_key_pem, public_key_pem):
        private_key = serialization.load_pem_private_key(private_key_pem, password=None)
        public_key = serialization.load_pem_public_key(public_key_pem)
        shared_key = private_key.exchange(ec.ECDH(), public_key)
        return shared_key
    @staticmethod
    def encrypt(data, iv, shared_key):
        try:
            logger.info(data)
            cipher = Cipher(algorithms.AES(shared_key), modes.CBC(iv.encode('utf-8')), backend=default_backend())
            padder = padding_v2.PKCS7(algorithms.AES.block_size).padder()
            padded_plaintext = padder.update(data.encode()) + padder.finalize()
            encryptor = cipher.encryptor()
            ciphertext = encryptor.update(padded_plaintext) + encryptor.finalize()
            return ciphertext.hex()
        except Exception as e:
            logger.exception(e)
            return b64encode(data, altchars="-_".encode("utf-8")).decode("utf-8")

    @staticmethod
    def decrypt(data, iv, shared_key):
        # data = '9539ae9360cd4d79450849b05f152bf707eecfa6a21fa54a70c8a7304a6b57f2c4e76fde00ee2fa718d77a47a61f3b1c99d466b0b28e09781b1f0d7e17322b32'
        cipher = Cipher(algorithms.AES(shared_key), modes.CBC(iv.encode('utf-8')), backend=default_backend())
        decryptor = cipher.decryptor()
        binary_data = bytes.fromhex(data)
        decrypted_padded_data = decryptor.update(binary_data) + decryptor.finalize()
        unpadder = padding_v2.PKCS7(algorithms.AES.block_size).unpadder()
        decrypted_data = unpadder.update(decrypted_padded_data) + unpadder.finalize()
        return decrypted_data.decode('utf-8')

class ShopeeFoodHMac:
    @staticmethod
    def hmac_sha256(hex_key: str, message: str) -> str:
        key_bytes = bytes.fromhex(hex_key)
        message_bytes = message.encode('utf-8')
        signature = hmac.new(key_bytes, message_bytes, hashlib.sha256).hexdigest()
        return signature

