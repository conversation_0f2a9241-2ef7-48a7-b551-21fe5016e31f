import json

import os
import time

from werkzeug.exceptions import BadRequest, InternalServerError
from app.errors.exceptions import BadRequest as UrBoxBadRequest
from loguru import logger

from app.config import config_app
from app.const import PROCESS_SENT, GET_CODE_WEBHOOK_SUCCESS, STATUS_OFF, PROCESS_SUCCESS, PROCESS_FAIL, \
    VGS_GET_CODE_WEBHOOK_SUCCESS, GET_CODE_WEBHOOK_FAILED, STATUS_ON, PROCESS_NEW, SUPPLIER_ORDER_PROCESS_SUCCESS, \
    SUPPLIER_ORDER_PROCESS_FAIL
from app.extensions import kafka_producer
from app.helper import Helper
from app.inum import GetCodeType
from app.libs.apis.a21api import A21Api
from app.libs.apis.third_party_api.behavior_grab_api import BehaviorGrabApi
from app.libs.apis.third_party_api.behavior_vietguys_api import BehaviorVietGuysApi
from app.libs.apis.urcard_api import <PERSON><PERSON>rd<PERSON><PERSON>
from app.libs.base.utility import Utility
from app.repositories import order_detail_repo, order_repo, codex_repo
from app.security import UrboxTripleDes, VietGuysCipher
from app.services.telegram_service import add_message_to_queue
from app.telegram_bot import send_telegram_message_to_group, build_message_get_code_order_template


class Webhook(object):
    def grab_webhook(self, **kwargs):
        logger.info(f"Request: {kwargs}")
        if len(kwargs.get("txID")) == 0:
            raise BadRequest(
                description="grab txID is require"
            )

        if len(kwargs.get("partnerReference")) == 0:
            raise BadRequest(
                description="grab partnerReference is require"
            )

        order_detail = order_detail_repo.get_with_condition(**{
            "transaction_id": kwargs.get("partnerReference"),
            "order_code": kwargs.get("txID")
        })
        if order_detail is None:
            logger.info(f"Not found data with transaction_id:{kwargs.get('partnerReference')}")
            raise BadRequest(
                description=f"Not found data with transaction_id:{kwargs.get('partnerReference')}"
            )

        if order_detail.action != PROCESS_SENT:
            raise BadRequest(
                description="The transaction has been processed."
            )

        order = order_repo.get_by_id(order_detail.order_id)
        if order is None:
            raise BadRequest(
                description=f"Not found order id"
            )

        po_callback = {}
        try:
            if kwargs.get("txStatus") != "success":
                meta = kwargs.get('meta')
                logger.info(f"Grab meta webhook response: {meta}")
                return True
            vendor = BehaviorGrabApi()
            codex, response_data = vendor.get_gift_links(kwargs.get("txID"), kwargs.get("partnerReference"))
            logger.info(
                f'response grab {kwargs.get("txID")} - {kwargs.get("partnerReference")}: {codex}, message: {response_data.get("message")}')
            list_codex = []
            created = int(time.time())
            for code in codex:
                list_codex.append(dict(
                    product_id=order.product_id,
                    supplier_id=order.supplier_id,
                    codex=code.get("codex"),
                    codex_int=Utility.string_folding(code.get("codex")),
                    serial=code.get("serial"),
                    expired_time=code.get("expired"),
                    partner_codex_id=code.get("partner_codex_id") or "",
                    product_parent_id=order.product_parent_id,
                    debt_recognition=order.debt_recognition,

                    is_export_codex=STATUS_ON,
                    hold_transaction="",
                    process=PROCESS_NEW,
                    code_type=2,  # who export
                    transaction_id='',
                    is_redis=STATUS_OFF,
                    is_giftcode=STATUS_OFF,
                    created=created,
                    updated=created,
                ))

            list_code = self.unique_codex_by_product_parent_id(
                list_codex, order.product_parent_id
            )
            quantity_success = len(list_code)

            self.update_supplier_order_log(order_detail.supplier_order_id, quantity_success,
                                           order_detail.quantity - quantity_success)

            # insert many and insert log
            if quantity_success > 0:
                codex_data = {
                    "po_code": order.po_code,
                    "request_id": order.request_id,
                    "transaction_id": kwargs.get("partnerReference"),
                    "partner_transaction_id": kwargs.get("txID") or "",
                }
                self.save_codex_to_database(list_code, codex_data)

            po_callback = self.callback(order.request_id, order.po_code, {
                "product_id": order.product_id,
                "product_request_id": order.product_request_id,
                "quantity": quantity_success,
                "order_id": order.id,
                "po_code": order.po_code,
            }, response_data.get('message'))

            self.update_quantity_stock(order.product_id, quantity_success)
            self.create_supplier_order_detail_log(
                codes=list_code,
                order_id=order_detail.supplier_order_id,
                product_id=order.product_id,
                money=order.amount,
                product_code=order.product_code,
                get_transaction_id=kwargs.get("partnerReference"),
                response_transaction_id=kwargs.get("txID")
            )

            order_detail.action = PROCESS_SUCCESS
            self.update_supplier_order_log(order_detail.supplier_order_id, quantity_success,
                                           order_detail.quantity - quantity_success)
            send_params = {
                "order_quantity": order.quantity,
                "po": order.po_code,
                "order_id": order.id,
                "supplier_id": order.supplier_id,
                "product_id": order.product_id,
                "product_code": order.product_code,
                "quantity": quantity_success
            }
            self.send_notice(**send_params)

        except Exception as e:
            order_detail.action = PROCESS_FAIL
            kwargs["message"] = str(e)
            logger.exception(e)
            send_telegram_message_to_group(build_message_get_code_order_template({
                "env": (os.environ.get("APP_CONFIG") or "UNKNOWN").upper(),
                "transaction_id": order_detail.transaction_id,
                "vendor_transaction_id": order_detail.order_code,
                "order_detail_id": order_detail.id,
                "order_id": order.id,
                "product_id": order.product_id,
                "error_message": kwargs["message"]
            }, False))

            raise InternalServerError(
                description="error when process"
            )
        finally:
            order_detail.updated = Helper.get_now_unix_timestamp()
            order_detail.callback_body = json.dumps(kwargs)
            order_detail.response_po = json.dumps(po_callback)
            order_detail.save()
        return True

    def vgs_webhook(self, **kwargs):
        logger.info(f"Request: {kwargs}")
        data = kwargs.get("data")
        if len(data.get("transaction_id")) == 0:
            logger.info('Transaction_id is require')
            raise BadRequest(
                description="Transaction_id is require"
            )
        po_callback = {}
        order_detail = order_detail_repo.get_with_condition(**{
            "transaction_id": data.get("tracking_id"),
            "order_code": data.get("transaction_id")

        })
        if order_detail is None:
            logger.info(f"Not found data with transaction_id: {data.get('transaction_id')}")
            raise BadRequest(
                description=f"Not found data with transaction_id: {data.get('transaction_id')}"
            )
        logger.info(f'order_detail: {order_detail.__dict__}')
        if order_detail.action != PROCESS_SENT:
            logger.info(f"The transaction has been processed.")
            raise BadRequest(
                description="The transaction has been processed."
            )
        order = order_repo.get_by_id(order_detail.order_id)
        if order is None:
            logger.info("Not found order")
            raise BadRequest(
                description="Not found order"
            )
        logger.info(f'order: {order.__dict__}')
        if kwargs.get('error') != 0:
            if len(order.request_id) > 0:
                self.callback(order.request_id, order.po_code, {
                    "product_id": order.product_id,
                    "product_request_id": order.product_request_id,
                    "quantity": 0,
                    "order_id": order.id,
                    "po_code": order.po_code,
                    "scheme_code": ""
                }, kwargs.get("message"))
            raise BadRequest(
                description=kwargs.get("message")
            )
        list_code_len = 0
        try:
            if kwargs.get("error") != VGS_GET_CODE_WEBHOOK_SUCCESS:
                raise UrBoxBadRequest(kwargs.get("message"))

            cards = data.get("cards")
            if len(cards) == 0:
                raise BadRequest(
                    description="{field_name} is required".format(field_name="Field cards")
                )

            if order_detail.quantity != len(cards):
                raise BadRequest(
                    description="Order quantity not equal to number of webhook."
                )

            vendor = BehaviorVietGuysApi()
            iv = data.get("iv")

            list_codex = []
            created = int(time.time())
            logger.info(cards)
            for card in cards:
                code_de = VietGuysCipher.decrypt(card.get("code"), iv, vendor.generate_shared_key())
                codex = UrboxTripleDes.encode(code_de)
                if code_de:
                    list_codex.append(dict(
                        product_id=order.product_id,
                        supplier_id=order.supplier_id,
                        codex=codex,
                        codex_int=Utility.string_folding(codex),
                        serial=card.get("seri"),
                        expired_time=Helper.time_to_unix_timestamp_with_format(card.get("expired_at") + " 23:59:59",
                                                                               "%Y-%m-%d %H:%M:%S"),
                        is_export_codex=STATUS_ON,
                        partner_codex_id="",
                        product_parent_id=order.product_parent_id,
                        debt_recognition=order.debt_recognition,
                        hold_transaction="",
                        process=PROCESS_NEW,
                        code_type=2,  # who export
                        transaction_id='',
                        is_redis=STATUS_OFF,
                        is_giftcode=STATUS_OFF,
                        created=created,
                        updated=created
                    ))
            logger.info(cards)
            list_code = self.unique_codex_by_product_parent_id(
                list_codex, order.product_parent_id
            )
            logger.info(list_code)
            list_code_len = len(list_code)
            # insert many and insert log
            if list_code_len > 0:
                codex_data = {
                    "po_code": order.po_code,
                    "request_id": order.request_id,
                    "transaction_id": data.get("tracking_id"),
                    "type": GetCodeType.GET_CODE_FROM_PORTAL.value,
                    "partner_transaction_id": data.get("transaction_id")
                }
                self.save_codex_to_database(list_code, codex_data)
            if len(order.request_id) > 0:
                po_callback = self.callback(order.request_id, order.po_code, {
                    "product_id": order.product_id,
                    "product_request_id": order.product_request_id,
                    "quantity": list_code_len,
                    "order_id": order.id,
                    "po_code": order.po_code,
                    "scheme_code": ""
                }, kwargs.get("message"))

            self.update_quantity_stock(order.product_id, list_code_len)

            self.create_supplier_order_detail_log(
                codes=list_code,
                order_id=order_detail.supplier_order_id,
                product_id=order.product_id,
                money=order.amount,
                product_code=order.product_code,
                get_transaction_id=data.get("tracking_id"),
                response_transaction_id=data.get("transaction_id")
            )

            order_detail.action = PROCESS_SUCCESS
            self.update_supplier_order_log(order_detail.supplier_order_id, list_code_len,
                                           order_detail.quantity - list_code_len)

        except Exception as e:
            order_detail.action = PROCESS_FAIL
            kwargs["message"] = str(e)
            logger.exception(e)
            send_telegram_message_to_group(build_message_get_code_order_template({
                "env": (os.environ.get("APP_CONFIG") or "UNKNOWN").upper(),
                "transaction_id": order_detail.transaction_id,
                "vendor_transaction_id": order_detail.order_code,
                "order_detail_id": order_detail.id,
                "order_id": order.id,
                "product_id": order.product_id,
                "error_message": kwargs["message"]
            }, False))

            raise InternalServerError(
                description="error when process"
            )
        finally:
            order_detail.updated = Helper.get_now_unix_timestamp()
            order_detail.callback_body = json.dumps(kwargs)
            order_detail.response_po = json.dumps(po_callback)
            order_detail.save()

            send_params = {
                "order_quantity": order.quantity,
                "po": order.po_code,
                "order_id": order.id,
                "supplier_id": order.supplier_id,
                "product_id": order.product_id,
                "product_code": order.product_code,
                "quantity": list_code_len
            }
            self.send_notice(**send_params)

        return True

    def webhook(self, **kwargs):
        logger.info(f"Webhook request: {kwargs}")
        if len(kwargs.get("transaction_id")) == 0:
            raise BadRequest(
                description="Transaction_id is require"
            )
        order_detail = order_detail_repo.get_by_tid(kwargs.get("transaction_id"))
        if order_detail is None:
            raise BadRequest(
                description=f"Not found data with transaction_id: {kwargs.get('transaction_id')}"
            )

        if order_detail.status != PROCESS_SENT:
            raise BadRequest(
                description="The transaction has been processed."
            )

        data = kwargs.get("detail")
        if kwargs.get("status") != GET_CODE_WEBHOOK_SUCCESS:
            return True
        order = order_repo.get_by_id(order_detail.order_id)
        if order is None:
            return False
        try:

            cards = data.get("codex")
            if len(cards) == 0:
                return False

            for card in cards:
                codex = UrboxTripleDes.encode(card)
                codex_repo.create(
                    product_id=order.product_id,
                    supplier_id=order.supplier_id,
                    codex=codex,
                    codex_int=Utility.string_folding(codex),
                    serial=card.get("serial"),
                    expired_time=card.get("expired"),
                    pin=None,
                    transaction_id=data.get("transaction_id"),
                    is_export_codex=STATUS_OFF,
                    product_parent_id=order.product_parent_id,
                )
            self.update_quantity_stock(order.product_id, len(cards))
        except Exception as e:
            logger.exception(e)
            kwargs["status"] = GET_CODE_WEBHOOK_FAILED
            kwargs["message"] = str(e)
            raise InternalServerError(
                description="error when process"
            )
        finally:
            order_detail.action = PROCESS_SUCCESS
            if kwargs.get("status") != GET_CODE_WEBHOOK_SUCCESS:
                order_detail.action = PROCESS_FAIL
            order_detail.updated = Helper.get_now_unix_timestamp()
            order_detail.save()

        return True

    def update_quantity_stock(self, product_id, quantity):
        kafka_producer.push(
            config_app.TOPIC_CHANGE_QUANTITY,
            {
                "type": "ADDITION",
                "payload": [{"quantity": quantity, "product_id": product_id}],
            },
        )

    def unique_codex_by_product_parent_id(self, list_codex, product_parent_id):
        list_unique_codex = []
        list_plain_codex_int = []
        if not list_codex:
            return list_unique_codex
        for codex in list_codex:
            if codex:
                list_plain_codex_int.append(str(codex.get("codex_int")))
        list_codex_exist = self.get_duplicate_codex_int_by_product_parent_id(list_plain_codex_int, product_parent_id)
        for codex in list_codex:
            if codex.get("codex") not in list_codex_exist:
                list_unique_codex.append(codex)
        return list_unique_codex

    def get_duplicate_codex_int_by_product_parent_id(self, list_codex_int, product_parent_id):
        list_codex_exist = codex_repo.get_by_codex_int_and_product_parent_id(list_codex_int, product_parent_id)
        list_plain_codex_exist = []
        for codex in list_codex_exist:
            list_plain_codex_exist.append(codex.codex)
        logger.info(f'A12:get_duplicate_codex_int_by_product_parent_id:list_codex_exist: {list_plain_codex_exist}')
        return list_plain_codex_exist

    def update_supplier_order_log(self, order_id, quantity_success, quantity_error):
        A21Api.update_order_log(
            id=order_id,
            quantity_success=quantity_success,
            quantity_error=quantity_error,
            process=SUPPLIER_ORDER_PROCESS_SUCCESS if quantity_success > 0 else SUPPLIER_ORDER_PROCESS_FAIL,
        )

    def create_supplier_order_detail_log(self, codes, order_id, product_id, money, product_code,
                                         get_transaction_id=None, response_transaction_id=None):
        if len(codes) > 0:
            request_data = []
            for code in codes:
                data = dict(
                    supplier_order_id=order_id or 0,
                    gift_detail_id=product_id or 0,
                    codex=code.get("codex") or "",
                    expired=code.get("expired_time") or 0,
                    serial=code.get("serial") or "",
                    money=int(money) or 0,
                    process=2,
                    isPayed=1,
                    product_code=product_code or "",
                    get_transaction_id=get_transaction_id,
                    response_transaction_id=response_transaction_id
                )
                request_data.append(data)
            A21Api.create_order_detail_log(request_data)

    def callback(cls, request_id, po_code, orders, message=''):
        return UrcardAPI.save(**{
            "request_id": request_id,
            "po_code": po_code,
            "orders": orders,
            "successful": True if message in ['', 'Success'] else False,
            "message": message,
        })

    def send_notice(self, **kwargs):
        add_message_to_queue(kwargs)

    def save_codex_to_database(self, codexs, codex_data=None):
        if len(codexs) == 0 or codex_data is None:
            return
        codex_entities = codex_repo.create_many(codexs)

        # Tạo dictionary từ codexs array để tối ưu hóa lookup (O(1) thay vì O(n))
        codex_partner_map = {item.get('codex'): item.get('partner_codex_id', '') for item in codexs}

        codex_items = []
        for codex_entity in codex_entities:
            # Sử dụng dictionary lookup thay vì nested loop để tối ưu hiệu suất
            partner_codex_id = codex_partner_map.get(codex_entity.get('codex'), '')

            codex_items.append({
                "codex_id": codex_entity.get('id'),
                "partner_codex_id": partner_codex_id
            })
        codex_data['codex_items'] = codex_items
        codex_data['type'] = GetCodeType.GET_CODE_FROM_PORTAL.value
        kafka_producer.push(
            config_app.TOPIC_INSERT_CODEX_DATA,
            {
                "type": "INSERT_CODEX_DATA",
                "payload": codex_data
            },
        )
