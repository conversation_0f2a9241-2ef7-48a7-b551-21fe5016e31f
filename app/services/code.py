#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import time

from loguru import logger
from werkzeug.exceptions import BadRequest

from app.config import config_app
from app.const import (
    STATUS_ON,
    STATUS_OFF,
    REDIS_SUPPLIER_KEY,
    REDIS_SUPPLIER_EXPIRED,
    PROCESS_NEW, REDIS_FLAG_GET_CODE_ORDER_KEY, PROCESS_SENT, LIMIT_NUMBER_OF_CODEX_ORDER,
)
from app.errors.exceptions import NotFound
from app.extensions import redis_client, kafka_producer
from app.inum import GetCodeType
from app.libs.apis.a21api import A21Api
from app.libs.apis.a6api import A6Api
from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
from app.libs.base.utility import Utility
from app.repositories import codex_repo, order_repo, order_detail_repo


class CodeDao(object):
    def __init__(self):
        self.product_id_need_get_code = []

    def get_new(self, args):
        response = []
        product_list = args.get("products")
        for product in product_list:
            logger.info(f"Lấy code với request: {product}")
            product_id = product.get("product_id")
            quantity = product.get("quantity")
            list_code = self.get_by_api_new(product_id, quantity)
            response.append(
                {
                    "product_id": int(product_id),
                    "codes": self.reformat_code(list_code)
                }
            )
        return response

    def get_by_api_new(self, product_id, quantity):
        suppliers = self.get_suppliers(product_id)
        logger.info(f"Product có thể lấy được code từ các supplier sau: {suppliers}")
        return self.get_by_supplier_new(suppliers, product_id, quantity)

    def get_by_supplier_new(self, suppliers, product_id, quantity):
        list_codex = []
        if suppliers is not None:
            for supplier in suppliers:
                if supplier.get('type') != 1:
                    logger.info(f'supplier-order: {supplier}')
                    continue
                if quantity == 0:
                    break
                if supplier["type"] != 1:
                    continue
                behavior = ContextCodeApi.get_behavior_by_supplier(supplier)
                if behavior is None:
                    continue
                logger.info(f'timestamp start get_by_supplier_new urbox {time.time()}')
                context = ContextCodeApi(behavior)
                list_code, response_data = context.get_code(
                    dict(
                        product_id=supplier.get("product_id"),
                        supplier_id=supplier.get("supplier_id"),
                        product_supplier_id=supplier.get("id"),
                        code_prefix=supplier.get("code_prefix"),
                        code_length=supplier.get("code_length"),
                        product_code=supplier.get("product_code"),
                        quantity=quantity,
                        price=supplier.get("supplier_value"),
                        product_parent_id=supplier.get("product_parent_id"),
                        max_codes_per_call=supplier.get("max_codes_per_call"),
                        order_id=0
                    )
                )
                logger.info(f'Lấy được {len(list_code)} code từ supplier {supplier.get("supplier_id")}')
                quantity -= len(list_code)
                logger.info(f'timestamp start save_code_to_database urbox {time.time()}')
                list_supplier_code = self.save_code_to_database(
                    product_id=product_id,
                    supplier_id=supplier.get("supplier_id"),
                    code_type=supplier.get("whoexport"),
                    list_code=list_code,
                    product_parent_id=supplier.get("product_parent_id"),
                    debt_recognition=supplier.get("debt_recognition")
                )
                codex_data = {
                    "po_code": "",
                    "request_id": "",
                    "transaction_id": response_data.get("transaction_id"),
                    "type": GetCodeType.GET_CODE_FROM_PORTAL.value,
                    'codex_items': [{
                        "codex_id": code.get("id"),
                        "partner_codex_id": ""
                    } for code in list_supplier_code]
                }
                self.insert_codex_data(codex_data)
                logger.info(f'timestamp after save_code_to_database urbox {time.time()}')
                list_codex += list_supplier_code
                logger.info(f'Lưu được {len(list_supplier_code)} code từ supplier {supplier.get("supplier_id")}')
        return list_codex

    def save_code_to_database(self, product_id, supplier_id, code_type, list_code, product_parent_id, debt_recognition):
        list_codex = []
        created = int(time.time())
        for code in list_code:
            list_codex.append(dict(
                product_id=product_id,
                supplier_id=supplier_id,
                codex=code.get("codex"),
                serial=code.get("serial"),
                pin=code.get("pin"),
                hold_transaction="",
                codex_int=code.get("codex_int") or Utility.string_folding(code.get("codex") or None),
                process=PROCESS_NEW,
                expired_time=code.get("expired") or 0,
                code_type=code_type,
                transaction_id='',
                is_redis=STATUS_OFF,
                is_export_codex=STATUS_ON,
                is_giftcode=STATUS_OFF,
                product_parent_id=product_parent_id,
                debt_recognition=debt_recognition,
                created=created,
                updated=created,
            ))
        codex = codex_repo.create_many(list_codex)
        return codex

    def reformat_code(self, list_code):
        output = []
        for item in list_code:
            code = dict(
                id=item.get("id") or "",
                codex=item.get("codex") or "",
                serial=item.get("serial") or "",
                pin=item.get("pin") or "",
                debt_recognition=item.get("debt_recognition") or 0,
                expired=int(item.get("expired_time")) if item.get("expired_time") else 0
            )
            output.append(code)
        return output

    def get_suppliers(self, product_id):
        suppliers = self.get_suppliers_on_redis(product_id)
        if suppliers is None:
            suppliers = self.get_suppliers_by_api(product_id)
            self.set_suppliers_on_redis(product_id, suppliers)
        return suppliers

    def get_suppliers_on_redis(self, product_id):
        key = f"{REDIS_SUPPLIER_KEY}:{product_id}"
        suppliers = redis_client.get(key)
        if suppliers:
            return json.loads(suppliers.decode('utf8'))
        return None

    def get_suppliers_by_api(self, product_id):
        suppliers = A6Api.get_suppliers(product_id)
        return suppliers

    def set_suppliers_on_redis(self, product_id, suppliers):
        key = f"{REDIS_SUPPLIER_KEY}:{product_id}"
        redis_client.set(key, json.dumps(suppliers))
        redis_client.expire(key, REDIS_SUPPLIER_EXPIRED)

    def set_get_code_order_flag(self, product_id):
        key = f"{REDIS_FLAG_GET_CODE_ORDER_KEY}:{product_id}"
        redis_client.set(key, 1)
        redis_client.expire(key, 2 * 60)

    def get_code_order_flag(self, product_id):
        key = f"{REDIS_FLAG_GET_CODE_ORDER_KEY}:{product_id}"
        flag = redis_client.get(key)
        if flag:
            return True
        return False

    def clear_get_code_order_flag(self, product_id):
        key = f"{REDIS_FLAG_GET_CODE_ORDER_KEY}:{product_id}"
        redis_client.delete(key)

    def is_latest_order_running(self, product_id):
        order = order_repo.get_by_product_id(product_id)
        if not order:
            return False

        if order.status == 2:
            order_details = order_detail_repo.get_list_with_condition(**{
                "order_id": order.id
            })
            if len(order_details) == 0:
                return False

            for order_detail in order_details:
                if order_detail.action == PROCESS_SENT:
                    return True
        return False

    def get_code_by_order(self, **kwargs):
        logger.info(f"get code req: {kwargs}")
        if self.is_latest_order_running(kwargs.get("product_id")):
            raise BadRequest(
                description="a latest order with product_id is running"
            )
        if kwargs.get('quantity') <= 0:
            raise BadRequest(
                description="quantity must be greater zero"
            )

        suppliers = self.get_suppliers(kwargs.get("product_id"))
        logger.info(f"suppliers: {suppliers}")
        if not suppliers:
            raise BadRequest(
                description="not found suppliers"
            )
        supplier = None
        for sup in suppliers:
            if sup["id"] == kwargs.get("product_supplier_id"):
                supplier = sup
        if not supplier:
            raise BadRequest(
                description="supplier doesnt match"
            )
        if supplier["type"] != 2:
            raise BadRequest(
                description="product doesn't support get code by order"
            )

        if not supplier["supplier_value"]:
            raise BadRequest(
                description="supplier value must be required"
            )

        # get config from product supplier
        kwargs.update({
            "amount": supplier["supplier_value"],
            "product_code": supplier["product_code"],
            "product_parent_id": supplier["product_parent_id"],
            "supplier_id": supplier["supplier_id"],
            "debt_recognition": supplier["debt_recognition"]
        })

        order = order_repo.create(**kwargs)
        behavior = ContextCodeApi.get_behavior_by_supplier(supplier)
        if behavior is None:
            raise BadRequest(
                description="not found context with supplier"
            )
        context = ContextCodeApi(behavior)
        quantity = kwargs.get("quantity")
        max_codes_per_call = supplier['max_codes_per_call'] or LIMIT_NUMBER_OF_CODEX_ORDER
        while quantity > 0:
            quantity_need_get = quantity
            if quantity > max_codes_per_call:
                quantity_need_get = max_codes_per_call
            logger.info(f'order product:{supplier.get("product_id")} , quantity: {quantity_need_get}/{quantity}')
            context.get_code({
                "product_id": kwargs.get("product_id"),
                "quantity": quantity_need_get,
                'price': kwargs.get("amount"),
                "product_code": kwargs.get("product_code"),
                "product_supplier_id": kwargs.get("product_supplier_id"),
                "product_parent_id": kwargs.get("product_parent_id"),
                "supplier_id": kwargs.get("supplier_id"),
                "order_id": order.id,
                "max_codes_per_call": max_codes_per_call,
            })
            quantity -= quantity_need_get
        return order.dict_as_json()

    def urcard_order_code(self, **kwargs):
        if order_repo.check_exist_request_id(kwargs.get("request_id")):
            raise BadRequest(
                description="request id already exist"
            )

        logger.info(f"urcard order code req: {kwargs}")
        products = kwargs.get("products")
        if len(products) == 0:
            raise BadRequest(
                description="not found product_id"
            )

        orders = []
        for product in products:
            if product["product_id"] == 0:
                continue
            if product["quantity"] < 1:
                continue
            suppliers = self.get_suppliers(product.get("product_id"))
            logger.info(f"suppliers: {suppliers}")
            logger.info(f"product: {product}")
            if not suppliers:
                continue

            supplier = None
            for sup in suppliers:
                if sup["supplier_id"] == kwargs.get("supplier_id") and sup['status'] == 2:
                    supplier = sup
            if not supplier:
                continue

            # 2 là order trả sau(vgs), 3 là order trả realtime
            if supplier["type"] not in [2, 3]:
                continue

            meta_data = kwargs.get('extra_info')
            if meta_data is None:
                meta_data = {
                    "scheme_code": product.get('scheme_code') or ""
                }
            else:
                meta_data["scheme_code"] = product.get('scheme_code') or ""
            order = {
                "supplier_id": supplier["supplier_id"],
                "product_id": product["product_id"],
                "product_parent_id": supplier["product_parent_id"],
                "scheme_code": product.get('scheme_code') or '',
                "retry_transaction_id": product.get('retry_transaction_id') or '',
                "quantity": product["quantity"],
                "amount": supplier["supplier_value"],
                "debt_recognition": supplier["debt_recognition"],
                "request_id": kwargs.get("request_id"),
                "expired": product.get("expired") or None,
                "effective_date": product.get("effectiveDate") or None,
                "po_code": kwargs.get("po"),
                "product_request_id": product.get("product_request_id") or "",
                "meta_data": json.dumps(meta_data)
            }
            order_entity = order_repo.create(**order)
            orders.append(order_entity.dict_as_json())
            quantity = product["quantity"]
            max_codes_per_call = supplier.get('max_codes_per_call')
            kafka_producer.push(
                config_app.TOPIC_BEFORE_PO_ORDER_CODE,
                {
                    "type": "BEFORE_PO_ORDER_CODE",
                    "payload": {
                        "supplier": supplier,
                        "kwargs": {
                            "quantity": quantity,
                            "max_codes_per_call": max_codes_per_call,
                            "request_id": kwargs.get("request_id"),
                            "po": kwargs.get("po"),
                            "retry_transaction_id": order.get('retry_transaction_id'),
                            "extra_info": kwargs.get('extra_info') or None,
                            "scheme_code": product.get('scheme_code') or '',
                        },
                        "order_entity": order_entity.dict_as_json(),
                    },
                },
            )

        if len(orders) == 0:
            raise BadRequest(
                description="Không tìm thấy thông tin supplier hợp lệ"
            )

        return {
            "orders": orders,
            "request_id": kwargs.get("request_id"),
        }

    def urcard_retry_order_code(self, **kwargs):
        orders = order_repo.list_order_by_request_id(kwargs.get("request_id"))
        if not orders:
            raise BadRequest(
                description="request id not exist"
            )

        logger.info(f"urcard order code req: {kwargs}")
        products = kwargs.get("products")
        if len(products) == 0:
            raise BadRequest(
                description="not found product_id"
            )

        orders = []
        for product in products:
            if product["product_id"] == 0:
                continue
            if product["quantity"] < 1:
                continue
            suppliers = self.get_suppliers(product.get("product_id"))
            logger.info(f"suppliers: {suppliers}")
            logger.info(f"product: {product}")
            if not suppliers:
                continue

            supplier = None
            for sup in suppliers:
                if sup["supplier_id"] == kwargs.get("supplier_id") and sup['status'] == 2:
                    supplier = sup
            if not supplier:
                continue

            # 2 là order trả sau(vgs), 3 là order trả realtime
            if supplier["type"] not in [2, 3]:
                continue

            meta_data = kwargs.get('extra_info')
            if meta_data is None:
                meta_data = {
                    "scheme_code": product.get('scheme_code') or ""
                }
            else:
                meta_data["scheme_code"] = product.get('scheme_code') or ""
            order = {
                "supplier_id": supplier["supplier_id"],
                "product_id": product["product_id"],
                "product_parent_id": supplier["product_parent_id"],
                "scheme_code": product.get('scheme_code') or '',
                "retry_transaction_id": product.get('retry_transaction_id') or '',
                "quantity": product["quantity"],
                "amount": supplier["supplier_value"],
                "debt_recognition": supplier["debt_recognition"],
                "request_id": kwargs.get("request_id"),
                "expired": product.get("expired") or None,
                "effective_date": product.get("effectiveDate") or None,
                "po_code": kwargs.get("po"),
                "product_request_id": product.get("product_request_id") or "",
                "meta_data": json.dumps(meta_data)
            }
            quantity = product["quantity"]
            max_codes_per_call = supplier.get('max_codes_per_call')
            kafka_producer.push(
                config_app.TOPIC_BEFORE_PO_ORDER_CODE,
                {
                    "type": "BEFORE_PO_ORDER_CODE",
                    "payload": {
                        "supplier": supplier,
                        "kwargs": {
                            "quantity": quantity,
                            "max_codes_per_call": max_codes_per_call,
                            "request_id": kwargs.get("request_id"),
                            "po": kwargs.get("po"),
                            "retry_transaction_id": order.get('retry_transaction_id'),
                            "extra_info": kwargs.get('extra_info') or None,
                            "scheme_code": product.get('scheme_code') or '',
                        },
                        "order_entity": {},
                    },
                },
            )

        if len(orders) == 0:
            raise BadRequest(
                description="Không tìm thấy thông tin supplier hợp lệ"
            )

        return {
            "orders": orders,
            "request_id": kwargs.get("request_id"),
        }

    def retry_get_code(self, **kwargs):
        transaction_id = kwargs.get("transaction_id")
        if transaction_id is None:
            raise NotFound("Không tìm thấy transaction")
        supplier_order = A21Api.get_supplier_order(transaction_id)
        logger.info('supplier_order: {}'.format(supplier_order))
        if supplier_order is False or supplier_order.get('status') != 2:
            raise NotFound("Không tìm thấy supplier order")
        if supplier_order.get('process') != 4:
            raise NotFound("Supplier order process not valid")

        product_id = supplier_order.get("product_id") or 0
        logger.info(product_id)
        product_suppliers = self.get_product_suppliers_by_product_id(product_id)
        logger.info(product_suppliers)
        supplier = next((item for item in product_suppliers if
                         item["supplier_id"] == supplier_order.get('supplier_id') and item['status'] == 2), None)

        if supplier is None:
            raise NotFound("Không tìm thấy supplier hợp lệ")

        behavior = ContextCodeApi.get_behavior_by_supplier(supplier)

        if behavior is None:
            raise NotFound("Không tìm thấy behavior hợp lệ")
        context = ContextCodeApi(behavior)
        codes_res, response_data = context.get_code(
            dict(
                product_id=supplier.get("product_id"),
                supplier_id=supplier.get("supplier_id"),
                product_supplier_id=supplier.get("id"),
                code_prefix=supplier.get("code_prefix"),
                code_length=supplier.get("code_length"),
                product_code=supplier.get("product_code"),
                quantity=supplier_order.get('quantity'),
                price=supplier.get("supplier_value"),
                product_parent_id=supplier.get("product_parent_id"),
                max_codes_per_call=supplier.get("max_codes_per_call"),
                order_id=0,
                retry_transaction_id=transaction_id,
                supplier_order_id=supplier_order.get('id'),
            )
        )
        logger.info(codes_res)
        codes = codes_res
        codexs = self.save_retry_code_to_database(
            product_id,
            supplier["supplier_id"],
            supplier["whoexport"],
            codes,
            supplier.get("product_parent_id"),
            supplier.get("debt_recognition"),
        )
        gotten_quantity = len(codexs)
        logger.info(gotten_quantity)
        if gotten_quantity > 0:
            codex_data = {
                "codex_items": [{
                    "codex_id": codex.id,
                    "partner_codex_id": ""
                } for codex in codexs],
                "transaction_id": transaction_id,
                "type": GetCodeType.GET_CODE_FROM_API.value
            }
            self.insert_codex_data(codex_data)
            self.update_quantity_stock(product_id, gotten_quantity)
        return gotten_quantity

    @classmethod
    def get_product_suppliers_by_product_id(cls, product_id):
        return A6Api.get_suppliers(product_id) or None

    @classmethod
    def save_retry_code_to_database(
            cls, product_id, supplier_id, code_type, codes, product_parent_id, debt_recognition
    ):
        codexs = []
        for code in codes:
            codex = codex_repo.create(
                product_id=product_id,
                supplier_id=supplier_id,
                codex=code.get("codex"),
                codex_int=code.get("codex_int"),
                serial=code.get("serial"),
                pin=code.get("pin"),
                expired_time=code.get("expired"),
                code_type=code_type,
                product_parent_id=product_parent_id,
                debt_recognition=debt_recognition,
            )
            if codex is not None:
                codexs.append(codex)
        return codexs

    @classmethod
    def update_quantity_stock(cls, product_id, quantity):
        kafka_producer.push(
            config_app.TOPIC_CHANGE_QUANTITY,
            {
                "type": "ADDITION",
                "payload": [{"quantity": quantity, "product_id": product_id}],
            },
        )

    def insert_codex_data(cls, codex_data):
        kafka_producer.push(
            config_app.TOPIC_INSERT_CODEX_DATA,
            {
                "type": "INSERT_CODEX_DATA",
                "payload": codex_data
            },
        )
