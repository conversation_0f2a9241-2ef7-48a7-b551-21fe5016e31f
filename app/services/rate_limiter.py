import time
import datetime

from loguru import logger

from app.extensions import redis_client

# Lua script for atomic INCR and EXPIRE operations
# KEYS[1]: The counter key for the current minute and request_name
# ARGV[1]: The TTL for the counter key in seconds
LUA_INCR_SCRIPT_CONTENT = """
local current_minute_key = KEYS[1]
local ttl = tonumber(ARGV[1])

local count = redis.call("INCR", current_minute_key)

if count == 1 then
    redis.call("EXPIRE", current_minute_key, ttl)
end

return count
"""

class RateLimiter:
    def __init__(self, default_limit_per_minute: int = 50,
                 counter_ttl_seconds: int = 120):
        """
        Initializes the SyncRateLimiter.

        :param default_limit_per_minute: Default limit if request_name not in limits_config.
        :param counter_ttl_seconds: TTL for Redis counter keys (e.g., 120 seconds).
        """
        self.default_limit_per_minute = default_limit_per_minute
        self.counter_ttl_seconds = counter_ttl_seconds
        self.redis_client = redis_client
        self.lua_incr_script = None

            # Register <PERSON>a script
        self.lua_incr_script = self.redis_client.register_script(LUA_INCR_SCRIPT_CONTENT)

    def _get_minute_key(self, request_name: str, dt_utc: datetime.datetime) -> str:
        """Generates a unique Redis key for a given request_name and UTC minute."""
        return f"A12:RATE_LIMIT:{request_name}:{dt_utc.strftime('%Y%m%d%H%M')}"

    def wait_if_needed(self, request_name: str, limit_per_minute: int):
        """
        Checks if the request for the given request_name is within limits.
        If over limit, blocks (sleeps) until the next minute, then retries.
        The method returns only when the request is allowed to proceed.
        """
        if not self.redis_client or not self.lua_incr_script:
            logger.error("Redis client or Lua script not initialized. Rate limiting disabled.")
            # Fail open or raise an exception depending on desired behavior for uninitialized limiter
            # For now, let's fail open with a warning, but in production, this might be an error.
            return

        limit = limit_per_minute or self.default_limit_per_minute

        while True:
            now_utc = datetime.datetime.utcnow()
            current_minute_redis_key = self._get_minute_key(request_name, now_utc)

            current_count = -1 # Default to an invalid count
            try:
                current_count = self.lua_incr_script(keys=[current_minute_redis_key], args=[self.counter_ttl_seconds])
            except Exception as e:
                logger.error(f"Redis error executing Lua script for '{request_name}': {e}. Allowing request (fail-open).", exc_info=True)
                # Fail-open strategy if Redis script fails. Consider alternatives for production.
                return

            if current_count <= limit:
                logger.info(f"Request '{request_name}' (count: {current_count}/{limit}) allowed in minute {now_utc.strftime('%Y%m%d%H%M')}.")
                return # Allowed to proceed
            else:
                # Over limit for the current minute. We need to wait.
                # First, decrement the counter because this request instance will wait and not use a slot in *this* minute.
                try:
                    self.redis_client.decr(current_minute_redis_key)
                    logger.debug(f"Decremented counter for '{current_minute_redis_key}' as request '{request_name}' will wait.")
                except Exception as e:
                    logger.error(f"Redis error decrementing counter for '{request_name}': {e}. Count might be slightly off.", exc_info=True)
                    # Continue to sleep and retry, but log the issue.

                # Calculate wait time until the start of the next UTC minute
                next_minute_start_utc = (now_utc.replace(second=0, microsecond=0) +
                                         datetime.timedelta(minutes=1))
                wait_duration = next_minute_start_utc - now_utc
                sleep_seconds = wait_duration.total_seconds()

                if sleep_seconds > 0.000001: # Check for a meaningful duration
                    logger.info(f"Request '{request_name}' (count: {current_count}/{limit}) over limit for minute {now_utc.strftime('%Y%m%d%H%M')}. "
                                f"Waiting {sleep_seconds:.6f} seconds until {next_minute_start_utc.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]} UTC.")
                    time.sleep(sleep_seconds)
                else:
                    # Negligible wait, or we might have just crossed the minute boundary during calculations.
                    # Loop will retry immediately, effectively for the new minute.
                    logger.info(f"Request '{request_name}' over limit. Negligible wait time. Retrying for new minute.")
                    # A very small sleep can prevent tight loops if time calculations are tricky at boundaries.
                    time.sleep(0.001) # Sleep 1ms to ensure `now_utc` updates if we are extremely close to boundary

            # Loop continues, now_utc will be re-evaluated for the (potentially) new minute.
rate_limiter = RateLimiter()