import json
import os
from app.extensions import redis_client
REDIS_KEY_STORE_MESSAGE_ID = "A12:TELEGRAM:MESSAGE_ID"
REDIS_KEY_STORE_FLAG = "A12:TELEGRAM:FLAG"
REDIS_KEY_STORE_MESSAGE = "A12:TELEGRAM:ORDER_MESSAGE"
REDIS_KEY_STORE_QUANTITY = "A12:TELEGRAM:ORDER_QUANTITY"

def add_message_to_queue(data):
    if data:
        content = json.dumps({
            "order_id": data.get('order_id'),
            "env": (os.environ.get("APP_CONFIG") or "UNKNOWN").upper(),
            "supplier_id": data.get('supplier_id'),
            "order_quantity": data.get('order_quantity'),
            "product_id": data.get('product_id'),
            "product_code": data.get('product_code'),
            "po": data.get('po'),
        })
        list_code_len = data.get('quantity')
        redis_key = get_redis_key_for_order(data.get('order_id'))
        redis_client.set(redis_key, content, 3600 * 2)
        # Cập nhật tổng số lượng code đã nhận
        if list_code_len:
            update_total_receive_count(f"{REDIS_KEY_STORE_QUANTITY}:{data.get('order_id')}", list_code_len)


def update_total_receive_count(key, value, expire_seconds=3600*2):
    redis_key = '{}'.format(key)
    if not redis_client.exists(redis_key):
        redis_client.set(redis_key, value, expire_seconds)
    else:
        redis_client.incrby(redis_key, value)
        redis_client.expire(redis_key, expire_seconds)

def get_redis_key_for_order(order_id):
    """Generates a consistent Redis key for an order_id."""
    return f"{REDIS_KEY_STORE_MESSAGE}:{order_id}"


