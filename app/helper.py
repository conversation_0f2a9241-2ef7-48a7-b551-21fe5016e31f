#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from OpenSSL.crypto import load_privatekey, FILETYPE_PEM, sign
from base64 import b64encode
from time import time, mktime
from datetime import datetime, timedelta
import pytz
import calendar

from app.const import (
    LOCAL_TIME_ZONE,
)


class SignatureHelper(object):
    @classmethod
    def generate_signature(cls, private_key, data, is_sha1=False):
        sha_type = "sha1" if is_sha1 else "sha256"
        pkey = load_privatekey(FILETYPE_PEM, private_key)
        signature = sign(pkey, data, sha_type)
        return b64encode(signature).decode("utf-8")


class DatetimeDao:
    @staticmethod
    def get_now():
        return datetime.now()

    @staticmethod
    def get_now_unixtimestamp():
        return int(time())

    @staticmethod
    def get_now_unixtimestamp_miliseconds():
        return time()

    @staticmethod
    def get_month_len(year: int, month: int):
        day_range = calendar.monthrange(year, month)
        return max(day_range)

    @staticmethod
    def add_day_from_now(days):
        os.environ["TZ"] = LOCAL_TIME_ZONE
        now = DatetimeDao.get_now()
        time_plus = now + timedelta(days=days)
        time_plus = time_plus.replace(minute=59, hour=23, second=59)
        unix_timestamp = time_plus.strftime("%s")
        return int(unix_timestamp)


class Helper(object):
    @classmethod
    def get_now_unix_timestamp(cls):
        return int(time())

    @classmethod
    def get_iso_time(cls):
        my_date = datetime.now()
        return str(my_date.strftime("%Y-%m-%dT%H:%M:%S.%f%z"))[0:23] + "Z"

    @classmethod
    def iso_format(cls, unix_timestamp):
        if unix_timestamp is None:
            return ""
        tz = pytz.timezone("Asia/Ho_Chi_Minh")
        result = datetime.fromtimestamp(int(unix_timestamp), tz).isoformat()
        return result[0:19] + ".000Z"

    @classmethod
    def time_to_unix_timestamp(cls, time_value) -> int:
        return int(mktime(datetime.strptime(time_value, "%d/%m/%Y").timetuple()))

    @classmethod
    def time_to_unix_timestamp_with_format(cls, time_value, format_time) -> int:
        return int(mktime(datetime.strptime(time_value, format_time).timetuple()))
