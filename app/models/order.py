#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import STATUS_ON
from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class Order(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "order"

    id = db.Column(db.Integer, primary_key=True)
    quantity = db.Column(db.Integer, index=False, unique=False, nullable=False)
    amount = db.Column(db.Integer, index=False, unique=False, nullable=False)
    product_code = db.Column(db.String(256), index=False, unique=False, nullable=True)
    product_id = db.Column(db.Integer, index=False, unique=False, nullable=True)
    supplier_id = db.Column(db.Integer, index=False, unique=False, nullable=True)
    product_parent_id = db.Column(db.Integer, index=False, unique=False, nullable=True)
    status = db.Column(db.Integer, index=False, unique=False, nullable=True)
    debt_recognition = db.Column(db.Integer, index=False, unique=False, nullable=True)
    effective_date = db.Column(db.Integer, index=False, unique=False, nullable=True)
    expired = db.Column(db.Integer, index=False, unique=False, nullable=True)
    request_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    po_code = db.Column(db.String(256), index=False, unique=False, nullable=True)
    product_request_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    meta_data = db.Column(db.String, index=False, unique=False, nullable=True)
    created = db.Column(db.Integer, index=False, unique=False, nullable=False)
    updated = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.quantity = kwargs.get("quantity") or 0
        self.amount = kwargs.get("amount") or 0
        self.product_code = kwargs.get("product_code") or 0
        self.product_id = kwargs.get("product_id") or 0
        self.supplier_id = kwargs.get("supplier_id") or 0
        self.product_parent_id = kwargs.get("product_parent_id") or 0
        self.status = kwargs.get("status") or STATUS_ON
        self.debt_recognition = kwargs.get("debt_recognition") or 1
        self.effective_date = kwargs.get("effective_date") or None
        self.expired = kwargs.get("expired") or None
        self.request_id = kwargs.get("request_id") or ''
        self.po_code = kwargs.get("po_code") or ''
        self.meta_data = kwargs.get("meta_data") or ''
        self.product_request_id = kwargs.get("product_request_id") or ""
        self.created = kwargs.get("created") or 0
        self.updated = kwargs.get("updated") or 0
