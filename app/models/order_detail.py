#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import PROCESS_SENT, STATUS_ON
from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class OrderDetail(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "order_detail"

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    supplier_order_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    order_code = db.Column(db.String(255), index=False, unique=False, nullable=True)
    request = db.Column(db.String(16000000), index=False, unique=False, nullable=False)
    response = db.Column(db.String(16000000), index=False, unique=False, nullable=False)
    response_po = db.Column(db.String(16000000), index=False, unique=False, nullable=False)
    transaction_id = db.Column(db.String(255), index=False, unique=False, nullable=False)
    status = db.Column(db.Integer, index=False, unique=False, nullable=False)
    callback_body = db.Column(db.String(16000000), index=False, unique=False, nullable=False)
    header = db.Column(db.String(16000000), index=False, unique=False, nullable=False)
    request_url = db.Column(db.String(255), index=False, unique=False, nullable=False)
    http_code = db.Column(db.Integer, index=False, unique=False, nullable=False)
    action = db.Column(db.Integer, index=False, unique=False, nullable=False)
    created = db.Column(db.Integer, index=False, unique=False, nullable=False)
    updated = db.Column(db.Integer, index=False, unique=False, nullable=False)
    quantity = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.order_id = kwargs.get("order_id") or 0
        self.supplier_order_id = kwargs.get("supplier_order_id") or 0
        self.order_code = kwargs.get("order_code") or ""
        self.request = kwargs.get("request") or ''
        self.response = kwargs.get("response") or ''
        self.callback_body = kwargs.get("callback_body") or ''
        self.header = kwargs.get("header") or ''
        self.request_url = kwargs.get("request_url") or ''
        self.response_po = kwargs.get("response_po") or ''
        self.http_code = kwargs.get("http_code") or 0
        self.transaction_id = kwargs.get("transaction_id") or ''
        self.status = kwargs.get("status") or STATUS_ON
        self.action = kwargs.get("action") or PROCESS_SENT
        self.created = kwargs.get("created") or 0
        self.updated = kwargs.get("updated") or 0
        self.quantity = kwargs.get("quantity") or 0
