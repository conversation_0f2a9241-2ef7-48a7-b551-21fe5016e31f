#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import STATUS_ON
from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class Config(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "config"

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), index=False, unique=False, nullable=False)
    valuex = db.Column(db.Integer, index=False, unique=False, nullable=False)
    status = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.key = kwargs.get("key") or None
        self.valuex = kwargs.get("valuex") or 0
        self.status = kwargs.get("status") or STATUS_ON
