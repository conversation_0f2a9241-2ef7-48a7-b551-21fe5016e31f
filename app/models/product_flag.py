#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import STATUS_OFF, STATUS_ON
from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class ProductFlag(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "product_flag"

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    flag = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.product_id = kwargs.get("product_id") or 0
        self.flag = kwargs.get("flag") or 1

    def turn_off_flag(self):
        result = self.update(flag=STATUS_OFF)
        return result

    def turn_on_flag(self):
        result = self.update(flag=STATUS_ON)
        return result
