#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import STATUS_ON
from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class CodexData(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "codex_data"

    id = db.Column(db.Integer, primary_key=True)
    codex_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    type = db.Column(db.Integer, index=False, unique=False, nullable=False)
    status = db.Column(db.Integer, index=False, unique=False, nullable=False)
    request_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    transaction_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    po_code = db.Column(db.String(256), index=False, unique=False, nullable=True)
    partner_transaction_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    partner_codex_id = db.Column(db.String(256), index=False, unique=False, nullable=True)
    meta_data = db.Column(db.String, index=False, unique=False, nullable=True)
    created = db.Column(db.Integer, index=False, unique=False, nullable=False)
    updated = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.codex_id = kwargs.get("codex_id") or 0
        self.type = kwargs.get("type") or 1
        self.status = kwargs.get("status") or STATUS_ON
        self.request_id = kwargs.get("request_id") or ''
        self.partner_transaction_id = kwargs.get("partner_transaction_id") or ''
        self.partner_codex_id = kwargs.get("partner_codex_id") or ''
        self.transaction_id = kwargs.get("transaction_id") or ''
        self.meta_data = kwargs.get("meta_data") or ''
        self.po_code = kwargs.get("po_code") or ''
        self.created = kwargs.get("created") or 0
        self.updated = kwargs.get("updated") or 0
