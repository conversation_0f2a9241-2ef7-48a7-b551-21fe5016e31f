#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import STATUS_OFF, STATUS_ON, PROCESS_NEW
from app.helper import Helper

from app.extensions import db
from app.models.abc import BaseModel, MetaBaseModel


class Codex(db.BaseModel, BaseModel, metaclass=MetaBaseModel):
    __tablename__ = "codex"

    id = db.Column(db.Integer, primary_key=True)
    supplier_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    product_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    cart_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    cart_detail_id = db.Column(db.Integer, index=False, unique=False, nullable=False)
    hold_transaction = db.Column(db.String(70), index=False, unique=False, nullable=False)
    serial = db.Column(db.String(255), index=False, unique=False, nullable=False)
    pin = db.Column(db.String(255), index=False, unique=False, nullable=False)
    # order_code = db.Column(db.String(255), index=False, unique=False, nullable=False)
    codex = db.Column(db.String(512), index=False, unique=False, nullable=False)
    codex_int = db.Column(db.Integer, index=False, unique=False, nullable=False)
    process = db.Column(db.Integer, index=False, unique=False, nullable=False)
    expired_time = db.Column(db.Integer, index=False, unique=False, nullable=False)
    updated = db.Column(db.Integer, index=False, unique=False, nullable=False)
    created = db.Column(db.Integer, index=False, unique=False, nullable=False)
    status = db.Column(db.Integer, index=False, unique=False, nullable=False)
    code_type = db.Column(db.Integer, index=False, unique=False, nullable=False)
    debt_recognition = db.Column(db.Integer, index=False, unique=False, nullable=False)
    transaction_id = db.Column(db.String(30), index=False, unique=False, nullable=False)
    is_redis = db.Column(db.Integer, index=False, unique=False, nullable=False)
    is_export_codex = db.Column(db.Integer, index=False, unique=False, nullable=False)
    is_giftcode = db.Column(db.Integer, index=False, unique=False, nullable=False)
    product_parent_id = db.Column(db.Integer, index=False, unique=False, nullable=False)

    def __init__(self, *args, **kwargs):
        self.supplier_id = kwargs.get("supplier_id") or 0
        self.product_id = kwargs.get("product_id") or 0
        self.cart_id = kwargs.get("cart_id") or 0
        self.cart_detail_id = kwargs.get("cart_detail_id") or 0
        self.hold_transaction = kwargs.get("hold_transaction") or ""
        self.serial = kwargs.get("serial") or ""
        self.pin = kwargs.get("pin") or ""
        self.codex = kwargs.get("codex") or ""
        # self.order_code = kwargs.get("order_code") or ""
        self.codex_int = kwargs.get("codex_int") or 0
        self.process = kwargs.get("process") or PROCESS_NEW
        self.expired_time = kwargs.get("expired_time") or 0
        self.status = kwargs.get("status") or STATUS_ON
        self.code_type = kwargs.get("code_type") or 0
        self.transaction_id = kwargs.get("transaction_id") or ""
        self.is_redis = kwargs.get("is_redis") or STATUS_OFF
        self.is_export_codex = kwargs.get("is_export_codex") or STATUS_OFF
        self.is_giftcode = kwargs.get("is_giftcode") or 0
        self.product_parent_id = kwargs.get("product_parent_id") or 0
        self.debt_recognition = kwargs.get("debt_recognition") or 1
        self.created = Helper.get_now_unix_timestamp()
        self.updated = Helper.get_now_unix_timestamp()

    def update_code_to_redis(self):
        result_updated = self.update(is_redis=STATUS_ON)
        return result_updated
