import mongoengine

from app.extensions import db_mongo  # noqa

import datetime


class LogsRequestApi(mongoengine.Document):
    meta = {"collection": "logs_request_api"}

    transaction_id = mongoengine.StringField(required=True)
    merchant_name = mongoengine.StringField(required=True)
    request_url = mongoengine.StringField(required=True)
    request_headers = mongoengine.DictField(default={})
    request_data = mongoengine.DictField(required=True, default={})
    request_round = mongoengine.StringField(required=False)
    response_headers = mongoengine.DictField(default={})
    response_data = mongoengine.DictField(default={})
    response_code = mongoengine.IntField(default=0)
    created = mongoengine.DateTimeField(default=datetime.datetime.utcnow)
    updated = mongoengine.DateTimeField(default=datetime.datetime.utcnow)
