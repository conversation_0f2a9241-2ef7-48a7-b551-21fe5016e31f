#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import time

import loguru

from app.extensions import redis_client

def remove_none_in_dict(data: dict) -> dict:
    return {key: value for key, value in data.items() if value is not None}

def set_rate_limit(product_id, request_per_second = 0, limit_time = 1):
    """
    Giới hạn số lượng request trong một khoảng thời gian với độ chính xác đến microsecond.
    Sử dụng thuật toán token bucket để đảm bảo phân phối đều các request.

    Args:
        product_id: ID của sản phẩm, dùng để tạo khóa Redis duy nhất
        request_per_second: Số lượng request tối đa cho phép trong khoảng thời gian (mặc định: 0, không giới hạn)
        limit_time: K<PERSON>ảng thời gian giới hạn tính bằng giây (mặc định: 1 giây)

    Returns:
        None
    """
    if request_per_second <= 0:
        return

    # Sử dụng time.time() để lấy timestamp với độ chính xác microsecond
    current_timestamp = time.time()
    window_start = current_timestamp - limit_time

    # Sử dụng khóa cố định không chứa timestamp để theo dõi trong cả cửa sổ thời gian
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:{product_id}"

    # Tính toán khoảng thời gian giữa các request (tính bằng giây)
    interval = limit_time / request_per_second

    # Xóa các mục cũ hơn cửa sổ thời gian
    redis_client.zremrangebyscore(lock_key, '-inf', window_start)

    # Lấy tất cả các timestamp trong cửa sổ hiện tại, sắp xếp theo thời gian
    entries = redis_client.zrange(lock_key, 0, -1, withscores=True)

    # Đếm số lượng request trong cửa sổ
    current_count = len(entries)

    if current_count < request_per_second:
        # Còn slot trống, thêm request hiện tại vào
        unique_key = f"{current_timestamp}_{time.time_ns() % 1000000}"

        try:
            # Cho Redis phiên bản mới
            redis_client.zadd(lock_key, {unique_key: current_timestamp})
        except Exception:
            try:
                # Cho Redis phiên bản cũ
                redis_client.zadd(lock_key, current_timestamp, unique_key)
            except Exception as e:
                loguru.logger.error(f"Error adding to rate limit tracker: {e}")

        # Đặt thời gian hết hạn cho khóa (gấp đôi thời gian cửa sổ để đảm bảo dữ liệu không bị mất)
        redis_client.expire(lock_key, limit_time * 2)
    else:
        # Đã đạt giới hạn, tính toán thời gian chờ dựa trên request cũ nhất
        if entries:
            # Lấy timestamp của request cũ nhất
            oldest_request_time = entries[0][1]

            # Tính thời điểm mà request tiếp theo có thể được thực hiện
            # (thời điểm request cũ nhất + khoảng thời gian cửa sổ)
            next_available_slot = oldest_request_time + limit_time

            # Tính thời gian cần chờ
            wait_time = next_available_slot - current_timestamp

            # Đảm bảo wait_time không âm và không quá lớn
            wait_time = max(0, min(wait_time, limit_time))

            loguru.logger.info(f"Rate limited for product {product_id}. Current count: {current_count}. Waiting for {wait_time:.6f} seconds.")

            if wait_time > 0:
                time.sleep(wait_time)

            # Sau khi đợi, thêm request hiện tại vào (sau khi đã xóa request cũ nhất)
            redis_client.zremrangebyrank(lock_key, 0, 0)  # Xóa request cũ nhất

            # Thêm request hiện tại với timestamp mới sau khi đã đợi
            new_timestamp = time.time()
            unique_key = f"{new_timestamp}_{time.time_ns() % 1000000}"

            try:
                redis_client.zadd(lock_key, {unique_key: new_timestamp})
            except Exception:
                try:
                    redis_client.zadd(lock_key, new_timestamp, unique_key)
                except Exception as e:
                    loguru.logger.error(f"Error adding to rate limit tracker after waiting: {e}")
        else:
            # Trường hợp hiếm gặp: entries rỗng nhưng current_count > 0
            # Có thể xảy ra do race condition, xử lý an toàn bằng cách đợi một khoảng thời gian nhỏ
            wait_time = interval
            loguru.logger.warning(f"Unexpected state in rate limiter for {product_id}. Waiting for {wait_time:.6f} seconds.")
            time.sleep(wait_time)

            # Thêm request hiện tại
            new_timestamp = time.time()
            unique_key = f"{new_timestamp}_{time.time_ns() % 1000000}"

            try:
                redis_client.zadd(lock_key, {unique_key: new_timestamp})
            except Exception:
                try:
                    redis_client.zadd(lock_key, new_timestamp, unique_key)
                except Exception as e:
                    loguru.logger.error(f"Error adding to rate limit tracker after waiting: {e}")


def set_rate_limit_per_minute(product_id, request_per_minute = 50):
    """
    Giới hạn số lượng request trong một phút với độ chính xác đến microsecond.
    Sử dụng thuật toán token bucket để đảm bảo phân phối đều các request.

    Args:
        product_id: ID của sản phẩm, dùng để tạo khóa Redis duy nhất
        request_per_minute: Số lượng request tối đa cho phép trong 1 phút (mặc định: 50)

    Returns:
        None
    """
    if request_per_minute <= 0:
        return

    # Sử dụng time.time() để lấy timestamp với độ chính xác microsecond
    current_timestamp = time.time()
    limit_time = 60  # 60 seconds = 1 minute
    window_start = current_timestamp - limit_time

    # Sử dụng khóa cố định không chứa timestamp để theo dõi trong cả cửa sổ 1 phút
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"

    # Tính toán khoảng thời gian giữa các request (tính bằng giây)
    interval = limit_time / request_per_minute

    # Xóa các mục cũ hơn cửa sổ thời gian
    redis_client.zremrangebyscore(lock_key, '-inf', window_start)

    # Lấy tất cả các timestamp trong cửa sổ hiện tại, sắp xếp theo thời gian
    entries = redis_client.zrange(lock_key, 0, -1, withscores=True)

    # Đếm số lượng request trong cửa sổ
    current_count = len(entries)

    if current_count < request_per_minute:
        # Còn slot trống, thêm request hiện tại vào
        unique_key = f"{current_timestamp}_{time.time_ns() % 1000000}"

        try:
            # Cho Redis phiên bản mới
            redis_client.zadd(lock_key, {unique_key: current_timestamp})
        except Exception:
            try:
                # Cho Redis phiên bản cũ
                redis_client.zadd(lock_key, current_timestamp, unique_key)
            except Exception as e:
                loguru.logger.error(f"Error adding to rate limit tracker: {e}")

        # Đặt thời gian hết hạn cho khóa (gấp đôi thời gian cửa sổ để đảm bảo dữ liệu không bị mất)
        redis_client.expire(lock_key, limit_time * 2)
    else:
        # Đã đạt giới hạn, tính toán thời gian chờ dựa trên request cũ nhất
        if entries:
            # Lấy timestamp của request cũ nhất
            oldest_request_time = entries[0][1]

            # Tính thời điểm mà request tiếp theo có thể được thực hiện
            # (thời điểm request cũ nhất + khoảng thời gian cửa sổ)
            next_available_slot = oldest_request_time + limit_time

            # Tính thời gian cần chờ
            wait_time = next_available_slot - current_timestamp

            # Đảm bảo wait_time không âm và không quá lớn
            wait_time = max(0, min(wait_time, limit_time))

            loguru.logger.info(f"Rate limited for product {product_id}. Current count: {current_count}. Waiting for {wait_time:.6f} seconds.")

            if wait_time > 0:
                time.sleep(wait_time)

            # Sau khi đợi, thêm request hiện tại vào (sau khi đã xóa request cũ nhất)
            redis_client.zremrangebyrank(lock_key, 0, 0)  # Xóa request cũ nhất

            # Thêm request hiện tại với timestamp mới sau khi đã đợi
            new_timestamp = time.time()
            unique_key = f"{new_timestamp}_{time.time_ns() % 1000000}"

            try:
                redis_client.zadd(lock_key, {unique_key: new_timestamp})
            except Exception:
                try:
                    redis_client.zadd(lock_key, new_timestamp, unique_key)
                except Exception as e:
                    loguru.logger.error(f"Error adding to rate limit tracker after waiting: {e}")
        else:
            # Trường hợp hiếm gặp: entries rỗng nhưng current_count > 0
            # Có thể xảy ra do race condition, xử lý an toàn bằng cách đợi một khoảng thời gian nhỏ
            wait_time = interval
            loguru.logger.warning(f"Unexpected state in rate limiter for {product_id}. Waiting for {wait_time:.6f} seconds.")
            time.sleep(wait_time)

            # Thêm request hiện tại
            new_timestamp = time.time()
            unique_key = f"{new_timestamp}_{time.time_ns() % 1000000}"

            try:
                redis_client.zadd(lock_key, {unique_key: new_timestamp})
            except Exception:
                try:
                    redis_client.zadd(lock_key, new_timestamp, unique_key)
                except Exception as e:
                    loguru.logger.error(f"Error adding to rate limit tracker after waiting: {e}")


def raw_response_message(resp):
    if not resp:
        return "Response rỗng"
    try:
        return resp.json()
    except (ValueError, json.JSONDecodeError):
        pass

    try:
        return resp.text
    except Exception as e:
        pass
    try:
        return resp.content.decode(errors="replace")
    except Exception:
        import base64
        return  base64.b64encode(resp.content).decode()
