#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app import models as m
from app.const import STATUS_OFF


class ProductFlagRepositories(object):
    def __init__(self):
        self.model = m.ProductFlag

    def create(self, product_id: int):
        creator = self.model(product_id=product_id)
        creator.save()
        return creator

    def get_product_flag(self, product_id: int):
        if not product_id:
            return None

        return self.model.query().filter(self.model.product_id == product_id).first()

    def get_flag(self, product_id: int):
        if not product_id:
            return STATUS_OFF

        product_flag = self.get_product_flag(product_id)
        if product_flag and product_flag.flag:
            return product_flag.flag

        return STATUS_OFF


product_flag_repo = ProductFlagRepositories()
