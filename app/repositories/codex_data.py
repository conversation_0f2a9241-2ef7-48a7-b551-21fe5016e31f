#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app import models as m
from app.const import STATUS_ON, PROCESS_SENT
from app.helper import Helper


class CodexDataRepositories(object):
    def __init__(self):
        self.model = m.CodexData

    # TODO: refactor all code
    def create(self, *args, **kwargs):
        creator = self.model(
            codex_id=kwargs.get("codex_id") or 0,
            type=kwargs.get("type") or 1,
            status=kwargs.get("status") or STATUS_ON,
            request_id=kwargs.get("request_id") or '',
            transaction_id=kwargs.get("request_id") or '',
            partner_codex_id=kwargs.get("partner_codex_id") or '',
            partner_transaction_id=kwargs.get("partner_transaction_id") or '',
            po_code=kwargs.get("po_code") or '',
            created=kwargs.get("created") or Helper.get_now_unix_timestamp(),
            updated=kwargs.get("updated") or Helper.get_now_unix_timestamp(),
            meta_data=kwargs.get("meta_data") or "",
        )
        creator.save()
        return creator

    def create_many(self, entities):
        data = []
        for entity in entities:
            entity.update({
                "created": Helper.get_now_unix_timestamp(),
                "updated": Helper.get_now_unix_timestamp(),
            })
            data.append(self.model(entity))
        creator = self.model.create_many(m.CodexData, entities)
        return creator

codex_data_repo = CodexDataRepositories()
