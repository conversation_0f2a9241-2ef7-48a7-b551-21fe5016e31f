#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app import models as m
from app.const import STATUS_ON, PROCESS_SENT
from app.helper import Helper


class OrderRepositories(object):
    def __init__(self):
        self.model = m.Order

    # TODO: refactor all code
    def create(self, *args, **kwargs):
        creator = self.model(
            quantity=kwargs.get("quantity") or 0,
            amount=kwargs.get("amount") or 0,
            campaign_id=kwargs.get("campaign_id") or 0,
            product_id=kwargs.get("product_id") or 0,
            supplier_id=kwargs.get("supplier_id") or 0,
            product_parent_id=kwargs.get("product_parent_id") or 0,
            status=kwargs.get("status") or STATUS_ON,
            debt_recognition=kwargs.get("debt_recognition") or 1,
            effective_date=kwargs.get("effective_date") or None,
            expired=kwargs.get("expired") or None,
            request_id=kwargs.get("request_id") or '',
            po_code=kwargs.get("po_code") or '',
            created=kwargs.get("created") or Helper.get_now_unix_timestamp(),
            updated=kwargs.get("updated") or Helper.get_now_unix_timestamp(),
            product_code=kwargs.get("product_code") or "",
            meta_data=kwargs.get("meta_data") or "",
            product_request_id=kwargs.get("product_request_id") or "",
        )
        creator.save()
        return creator

    def create_many(self, entities):
        data = []
        for entity in entities:
            entity.update({
                "created": Helper.get_now_unix_timestamp(),
                "updated": Helper.get_now_unix_timestamp(),
            })
            data.append(self.model(entity))
        creator = self.model.create_many(m.Order, entities)
        return creator

    def get_by_id(self, order_id):
        return self.model.query().filter(self.model.id == order_id).first()

    def get_by_product_id(self, product_id):
        return (self.model.query().
                filter(self.model.product_id == product_id).
                order_by(self.model.id.desc()).first())

    def check_exist_request_id(self, request_id):
        exists = self.model.query().filter(self.model.request_id == request_id, self.model.status == 2).first() is not None
        return exists

    def list_order_by_request_id(self, request_id):
        return self.model.query().filter(self.model.request_id == request_id, self.model.status == 2).all()

order_repo = OrderRepositories()
