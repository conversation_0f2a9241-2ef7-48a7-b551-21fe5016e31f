#!/usr/bin/python
# -*- coding: utf-8 -*-

from flask import make_response
from flask import jsonify


class Response:
    @staticmethod
    def errors(response_code, message, errors=[], code=None):
        if code is None:
            code = response_code

        return make_response(
            jsonify(
                dict(
                    success=False,
                    error=dict(
                        errors=errors,
                    ),
                    code=code,
                    message=message,
                )
            ),
            response_code,
        )

    @staticmethod
    def success(*args, **kwargs):
        success = kwargs.get("success") if kwargs.get("success") is not None else True
        message = kwargs.get("message") if kwargs.get("message") is not None else None
        meta = kwargs.get("meta") if kwargs.get("meta") is not None else None
        data = kwargs.get("data") if kwargs.get("data") is not None else None

        response = dict(success=success)

        if data is not None:
            response.update(data=data)
        if message is not None:
            response.update(message=message)
        if meta is not None:
            response.update(meta=meta)

        return make_response(jsonify(response))
