#!/usr/bin/python
# -*- coding: utf-8 -*-
import time

import requests
import logging

logger = logging.getLogger(__name__)


class RequestDao:
    def get_content(self, response):
        try:
            content = response.json()
        except Exception as e:
            content = response.content
        return content

    def get(self, url, params=None, headers=None):
        content = None
        if url is None or url == "":
            return
        start_time = time.time()
        try:
            session = requests.Session()
            response = session.get(url, params=params, headers=headers)
            if response.status_code == 200:
                content = self.get_content(response)
        except requests.ConnectionError as e:
            logger.exception(
                "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
            )
        except requests.Timeout as e:
            logger.exception("OOPS!! Timeout Error")
        except requests.RequestException as e:
            logger.exception("OOPS!! General Error")
        except Exception as e:
            logger.exception(e)

        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f'Request to {url} took {execution_time:.2f} seconds"')
        return content

    def post(self, url, params=None, headers=None):
        content = None
        if url is None or url == "":
            return
        start_time = time.time()
        try:
            session = requests.Session()
            response = session.post(url, json=params, headers=headers)
            if response.status_code == 200:
                content = self.get_content(response)
        except requests.ConnectionError as e:
            logger.exception(
                "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
            )
        except requests.Timeout as e:
            logger.exception("OOPS!! Timeout Error")
        except requests.RequestException as e:
            logger.exception("OOPS!! General Error")
        except Exception as e:
            logger.exception(e)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f'Request to {url} took {execution_time:.2f} seconds"')
        return content

    def upload(self, url, params=None, headers=None, files=None):
        content = None
        if url is None or url == "":
            return

        try:
            session = requests.Session()
            response = session.post(url, data=params, headers=headers, files=files)
            if response.status_code == 200:
                content = self.get_content(response)
        except requests.ConnectionError as e:
            logger.exception(
                "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
            )
        except requests.Timeout as e:
            logger.exception("OOPS!! Timeout Error")
        except requests.RequestException as e:
            logger.exception("OOPS!! General Error")
        except Exception as e:
            logger.exception(e)
        return content

    def put(self, url, params=None, headers=None):
        content = None
        if url is None or url == "":
            return
        start_time = time.time()
        try:
            session = requests.Session()
            response = session.put(url, json=params, headers=headers)
            if response.status_code == 200:
                content = self.get_content(response)
        except requests.ConnectionError as e:
            logger.exception(
                "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
            )
        except requests.Timeout as e:
            logger.exception("OOPS!! Timeout Error")
        except requests.RequestException as e:
            logger.exception("OOPS!! General Error")
        except Exception as e:
            logger.exception(e)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f'Request to {url} took {execution_time:.2f} seconds"')
        return content

    def patch(self, url, params=None, headers=None):
        response = None
        if url is None or url == "":
            return
        start_time = time.time()
        try:
            session = requests.Session()
            response = session.patch(url, json=params, headers=headers)
        except requests.ConnectionError as e:
            logger.exception(
                "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
            )
        except requests.Timeout as e:
            logger.exception("OOPS!! Timeout Error")
        except requests.RequestException as e:
            logger.exception("OOPS!! General Error")
        except Exception as e:
            logger.exception(e)
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f'Request to {url} took {execution_time:.2f} seconds"')
        return response

    def get_with_retry(self, url, params=None, headers=None, retry=3, backoff_factor=0.5):
        def backoff_func(backoff, number_retry):
            return backoff * (2 ** (number_retry - 1))

        content = None
        if url is None or url == "":
            return
        start_time = time.time()
        for i in range(retry):
            try:
                session = requests.Session()
                response = session.get(url, params=params, headers=headers)
                if response.status_code == 200:
                    content = self.get_content(response)
                    break
            except requests.ConnectionError as e:
                logger.exception(
                    "OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below."
                )
            except requests.Timeout as e:
                logger.exception("OOPS!! Timeout Error")
            except requests.RequestException as e:
                logger.exception("OOPS!! General Error")
            except Exception as e:
                logger.exception(e)
            time.sleep(backoff_func(backoff_factor, i + 1))
        end_time = time.time()
        execution_time = end_time - start_time
        logger.info(f'Request to {url} took {execution_time:.2f} seconds"')
        return content
