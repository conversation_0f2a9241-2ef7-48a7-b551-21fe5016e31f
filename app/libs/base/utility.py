#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import time
import uuid
from datetime import datetime, date, timedelta
from random import choice
from time import mktime, sleep

import pytz
import telegram
from loguru import logger

from app.const import (
    LOCAL_TIME_ZONE,
    REDIS_KEY_CONFIG_PREFIX,
    REDIS_KEY_CONFIG_EXPIRED,
    GENERATE_CODE_LENGTH_LIMIT,
    GENERATE_CODE_ALLOWED_CHARS,
    FLASK_CELERY_TASK_SLEEP, TELEGRAM_TOKEN, TELEGRAM_CHAT_ID,
)
from app.extensions import redis_client
from app.helper import DatetimeDao


class Utility:
    @staticmethod
    def add_days(source_date, days):
        time_tmp = source_date + timedelta(days)
        unix_time = time_tmp.strftime(
            "%s"
        )  # Second as a decimal number [00,61] (or Unix Timestamp)
        return int(unix_time)

    @staticmethod
    def add_months(source_date, months):
        month = source_date.month - 1 + months
        year = source_date.year + month // 12
        month = month % 12 + 1
        day = min(source_date.day, DatetimeDao.get_month_len(year, month))
        return date(year, month, day)

    @staticmethod
    def sub_months(source_date, months):
        month = source_date.month - 1 - months
        year = source_date.year + month // 12
        month = month % 12 + 1
        day = min(source_date.day, DatetimeDao.get_month_len(year, month))
        return date(year, month, day)

    @staticmethod
    def diff_month(start_date: datetime, end_date: datetime):
        number_months = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month)
        return number_months

    @staticmethod
    def diff_days(start_date: datetime, end_date: datetime):
        number_days = (end_date - start_date).days
        return number_days

    @staticmethod
    def sleep():
        sleep(FLASK_CELERY_TASK_SLEEP)

    @staticmethod
    def convert_datetime_to_unixtimestamp(datetime_value, datetime_format):
        os.environ["TZ"] = LOCAL_TIME_ZONE
        if not datetime_value or datetime_value == "0":
            return 0
        try:
            return int(mktime(datetime.strptime(datetime_value, datetime_format).timetuple()))
        except Exception as e:
            logger.exception(e)
        return 0

    @staticmethod
    def convert_unixtime_to_format_time(datetime_value, datetime_format, timezone=LOCAL_TIME_ZONE):
        if not datetime_value or datetime_value == "0" or datetime_format == 0:
            return ""
        try:
            # Convert UTC timestamp to datetime object
            dt_utc = datetime.utcfromtimestamp(int(datetime_value))
            # Convert to specified timezone (default is LOCAL_TIME_ZONE)
            tz = pytz.timezone(timezone)
            dt_with_tz = dt_utc.replace(tzinfo=pytz.UTC).astimezone(tz)
            # Format the datetime according to the specified format
            return dt_with_tz.strftime(datetime_format)
        except Exception as e:
            logger.exception(e)
        return 0

    @staticmethod
    def convert_format_time_to_unixtime(datetime_str, datetime_format):
        if not datetime_str or datetime_str == "":
            return 0
        try:
            return int(time.mktime(datetime.strptime(datetime_str, datetime_format).timetuple()))
        except Exception as e:
            print(e)
        return 0

    @staticmethod
    def get_random_character(
        quantity,
        character_length=GENERATE_CODE_LENGTH_LIMIT,
        character_allowed=GENERATE_CODE_ALLOWED_CHARS,
    ):
        random_characters = []
        for i in range(quantity):
            random_character = "".join(
                choice(character_allowed) for i in range(character_length)
            )
            random_characters.append(random_character)
        return random_characters

    @staticmethod
    def get_unique_of_list(items):
        items_unique_type_set = set(items)
        items_unique_type_list = list(items_unique_type_set)
        return items_unique_type_list

    @staticmethod
    def generate_uuid():
        uniq_id = uuid.uuid1().time
        return uniq_id

    @staticmethod
    def get_key(key, is_file=False):
        pass
        if is_file:
            try:
                key_path = "/home/<USER>/core/app/rsa_key/" + key
                key = open(key_path).read()
            except Exception as e:
                logger.exception(e)
                key = ""
        return key

    @staticmethod
    def get_config(key):
        return Utility.__get_config_on_redis(key)

    @staticmethod
    def __get_config_on_redis(key):
        redis_key = Utility.__generate_redis_key(key)
        config_value = redis_client.get(redis_key)
        if not config_value:
            Utility.__load_config_to_redis()
            config_value = redis_client.get(redis_key)
        return (
            int(config_value) if config_value is not None or config_value == "" else 0
        )

    @staticmethod
    def __load_config_to_redis():
        from app.repositories.config import config_repo

        configs = config_repo.get_configs()
        for config in configs:
            key = Utility.__generate_redis_key(config.key)
            redis_client.set(key, config.valuex)
            redis_client.expire(key, REDIS_KEY_CONFIG_EXPIRED)

    @staticmethod
    def __generate_redis_key(key):
        return "{prefix}{key}".format(prefix=REDIS_KEY_CONFIG_PREFIX, key=key)

    @staticmethod
    def uniqid(prefix="UB"):
        time_code = datetime.now(pytz.timezone(LOCAL_TIME_ZONE)).strftime("%Y%m%d")
        request_code = uuid.uuid4().node
        return prefix + time_code + str(request_code)

    @staticmethod
    def my_random_string(string_length=10):
        random = str(uuid.uuid4())  # Convert UUID format to a Python string.
        random = random.replace("-", "")  # Remove the UUID '-'.
        return random[0:string_length]  # Return the random string.

    @staticmethod
    def string_folding(string, int_length=1000000000):
        if not string:
            return 0

        multi = 1
        total = 0
        for i in range(0, len(string)):
            if i % 4 == 0:
                multi = 1
            else:
                multi = multi * 256
            total += ord(string[i]) * multi
        return total % int_length

    @staticmethod
    def send_telegram_message_to_group(message):
        try:
            message = f"[production] {message}"
            bot = telegram.Bot(token=TELEGRAM_TOKEN)
            bot.send_message(text=message, chat_id=TELEGRAM_CHAT_ID)
        except:
            logger.error('Lỗi gửi tin nhắn telegram bot')
