#!/usr/bin/python
# -*- coding: utf-8 -*-
from urbox_lib.const import SUPPLIER_CONTACT_EMAIL

from app.const import (
    SERVICE_DOMAIN_A18_URBOX,
    SERVICE_ACTION_A18_URBOX_USED_CODE,
    SERVICE_VERSION_A18_URBOX,
    SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_TYPE,
    SERVICE_ACTION_A18_URBOX_STATUS_CODE,
    SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_INFO,
    SERVICE_ACTION_A18_URBOX_UPDATE_CODE,
    SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_CROSS_CHECK,
    SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_PAYED,
    SERVICE_ACTION_A18_URBOX_GET_PRODUCT_BY_CODE,
    SERVICE_ACTION_A18_URBOX_GET_EMAIL_BY_JOB_AND_SUPPLIER,
    SERVICE_ACTION_A18_URBOX_SUPPLIER_INFO,
    SERVICE_ACTION_GET_SUPPLIER_ORDER_PROCESS_PENDING,
    SERVICE_ACTION_POST_SUPPLIER_ORDER_CREATE,
    SERVICE_ACTION_POST_SUPPLIER_ORDER_PROCESS_UPDATE,
    SERVICE_ACTION_A18_CREATE_SUPPLIER_ORDER,
    SERVICE_ACTION_A18_UPDATE_SUPPLIER_ORDER,
    SERVICE_ID,
    SERVICE_ACTION_A18_CREATE_MULTI_ORDER_DETAIL,
)
from app.libs.base.utility import Utility
from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import AbstractAApi

import time
import uuid

from app.security import UrboxTripleDes

request_dao = RequestDao()


class A18Api(AbstractAApi):
    @staticmethod
    def get_urbox_used_code(params):
        result = []
        query_params = dict(
            supplier_id=params.get("supplier_id"),
            page=params.get("page"),
            from_time=params.get("from_time"),
            to_time=params.get("to_time"),
        )
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_USED_CODE,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            paging = response.get("data").get("meta")
            if int(paging.get("last_page")) < params.get("page"):
                return None
            items = response.get("data").get("response")
            for item in items:
                code = dict(
                    transaction_id=item.get("code"),
                    money=item.get("money"),
                    supplier_id=item.get("supplier_id"),
                    used=item.get("used"),
                    title=item.get("title"),
                    product_id=item.get("product_id"),
                )
                result.append(code)
            data = [
                dict(item) for item in set(frozenset(tmp.items()) for tmp in result)
            ]
            return result
        return None

    @staticmethod
    def get_discount_type(params):
        result = []

        if params.get("product_ids") is None or len(params.get("product_ids")) == 0:
            return None

        query_params = dict(
            supplier_id=params.get("supplier_id"),
            reconciliation_type=params.get("type"),
        )
        counter = 0
        for product_id in params.get("product_ids"):
            query_params["product_ids['{}']".format(counter)] = product_id
            counter += 1

        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_TYPE,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            for item in response.get("data"):
                result.append(
                    dict(
                        group_id=item.get("discount_group_id"),
                        product_id=item.get("product_id"),
                        supplier_id=item.get("supplier_id"),
                    )
                )
            return result
        return None

    @staticmethod
    def get_status_code(params):
        result = []
        query_params = dict(data=params.get("data"))
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_STATUS_CODE,
        )

        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            for item in response.get("data"):
                result.append(
                    dict(
                        code=UrboxTripleDes.encode(item.get("code")),
                        money=item.get("money"),
                        status=item.get("status"),
                        note=item.get("message"),
                    )
                )
            result = [
                dict(code)
                for code in set(
                    frozenset(cross_check.items()) for cross_check in result
                )
            ]
            return result
        return None

    @staticmethod
    def get_discount(params):
        query_params = dict(
            supplier_id=params.get("supplier_id"),
            group_id=params.get("group_id"),
            reconciliation_type=params.get("reconciliation_type"),
            revenue=params.get("revenue"),
        )
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_GET_DISCOUNT_INFO,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            return dict(
                discount_type=data.get("discount_type"),
                discount_value=data.get("discount"),
            )
        return dict(discount_type=TYPE_DISCOUNT_CONSTANT, discount_value=0)

    @staticmethod
    def update_code_cross_check(params):
        query_params = dict(
            codes=params.get("codes"),
            type=SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_CROSS_CHECK,
        )

        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_UPDATE_CODE,
        )

        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            return True
        return False

    @staticmethod
    def update_code_payed(params):
        query_params = dict(
            codes=params.get("codes"),
            type=SERVICE_CONSTANT_A18_UPDATE_CODE_TYPE_PAYED,
        )

        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_UPDATE_CODE,
        )
        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            return True
        return False

    @staticmethod
    def get_product_ids_by_codes(supplier_id, transaction_ids):
        query_params = dict(data=[])

        for transaction_id in transaction_ids:
            query_params.get("data").append(
                dict(supplier_id=supplier_id, code=transaction_id)
            )
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_GET_PRODUCT_BY_CODE,
        )
        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            data = []
            items = response.get("data")
            for item in items:
                data.append(
                    dict(
                        product_id=item.get("product_id"),
                        used_time=item.get("using_time"),
                        transaction_id=item.get("code"),
                        delivery_time=item.get("delivery_time"),
                    )
                )
            return data
        return None

    @staticmethod
    def get_supplier_contacts(supplier_id):
        query_params = dict(supplier_id=supplier_id, job_type=SUPPLIER_CONTACT_EMAIL)
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_GET_EMAIL_BY_JOB_AND_SUPPLIER,
        )
        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            data = response.get("data").get("response")
            if len(data) == 0:
                return None

            contacts = [
                dict(
                    email=contact.get("email"),
                    name=contact.get("name"),
                )
                for contact in data
            ]
            return contacts
        return None

    @staticmethod
    def get_supplier(supplier_id):
        query_params = dict(
            supplier_id=supplier_id,
        )
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_URBOX_SUPPLIER_INFO,
        )
        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            data = response.get("data")
            return dict(
                title=data.get("title"),
                id=data.get("id"),
            )
        return None

    @staticmethod
    def get_supplier_order_file_name():
        query_params = dict()
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_GET_SUPPLIER_ORDER_PROCESS_PENDING,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )

        if A18Api.is_response_success(response):
            data = response.get("data").get("response")
            return data
        return []

    @staticmethod
    def create_codex_a18(*args, **kwargs):
        query_params = dict(
            codex=kwargs.get("codex"),
            supplier_order_id=kwargs.get("supplier_order_id"),
            expired=kwargs.get("expired_time"),
            money=0,
            process=kwargs.get("process"),
            isPayed=1,
            note=kwargs.get("note"),
            product_code=kwargs.get("product_code"),
            serial=kwargs.get("serial"),
        )

        query_params = A18Api.add_query_params(query_params)

        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_POST_SUPPLIER_ORDER_CREATE,
        )
        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A18Api.is_response_success(response):
            return response
        return response

    @staticmethod
    def update_process_supplier_order(*args, **kwargs):
        query_params = dict(
            process=kwargs.get("process"),
            id=kwargs.get("id"),
        )
        query_params = A18Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_POST_SUPPLIER_ORDER_PROCESS_UPDATE,
        )
        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        return response

    @staticmethod
    def create_order_log(*args, **kwargs):
        params = dict(
            product_supplier_id=kwargs.get("product_supplier_id")
            if kwargs.get("product_supplier_id") is not None
            else None,
            transaction_id=kwargs.get("transaction_id")
            if kwargs.get("transaction_id") is not None
            else None,
            supplier_id=kwargs.get("supplier_id")
            if kwargs.get("supplier_id") is not None
            else None,
            process=1,
            quantity=kwargs.get("quantity")
            if kwargs.get("quantity") is not None
            else 0,
            product_id=kwargs.get("product_id")
            if kwargs.get("product_id") is not None
            else None,
        )
        params = A18Api.add_query_params(params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_CREATE_SUPPLIER_ORDER,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        if A18Api.is_response_success(response):
            return response.get("data").get("id")
        return False

    @staticmethod
    def update_order_log(*args, **kwargs):
        params = dict(
            id=kwargs.get("id") if kwargs.get("id") is not None else None,
            quantity_success=kwargs.get("quantity_success")
            if kwargs.get("quantity_success") is not None
            else None,
            quantity_error=kwargs.get("quantity_error")
            if kwargs.get("quantity_error") is not None
            else None,
            process=3,
        )
        params = A18Api.add_query_params(params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_UPDATE_SUPPLIER_ORDER,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        return A18Api.is_response_success(response)

    @staticmethod
    def create_order_detail_log(data):
        # TODO: Tạm thời đặt _sid, _aid, _rid trên url, chờ a Thắng fix
        current_unixtimestamp = int(time.time())
        uuid_note = uuid.uuid4().node
        rid = "{time}{uuid}".format(time=current_unixtimestamp, uuid=uuid_note)
        query_params = "_rid={rid}&_aid={aid}&_sid={sid}".format(
            sid=SERVICE_ID, aid=0, rid=rid
        )

        params = A18Api.add_query_params(dict(data=data))

        url = "{domain}/{version}/{action}?{query}".format(
            domain=SERVICE_DOMAIN_A18_URBOX,
            version=SERVICE_VERSION_A18_URBOX,
            action=SERVICE_ACTION_A18_CREATE_MULTI_ORDER_DETAIL,
            query=query_params,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        return A18Api.is_response_success(response)
