#!/usr/bin/python
# -*- coding: utf-8 -*-
from app.const import (
    SERVICE_DOMAIN_A8_DOCUMENT,
    SERVICE_ACTION_A8_READ_EXCEL,
    SERVICE_VERSION_A8_DOCUMENT,
    SERVICE_ACTION_A8_UPLOAD,
    SERVICE_ACTION_A8_DOWNLOAD,
)
from app.libs.base.utility import Utility
from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import Abstract<PERSON>pi

request_dao = RequestDao()


class A8Api(AbstractAApi):
    @staticmethod
    def get_data_from_file(params, page=1):
        query_params = dict(name=params.get("name"), page=page)
        query_params = A8Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A8_DOCUMENT,
            version=SERVICE_VERSION_A8_DOCUMENT,
            action=SERVICE_ACTION_A8_READ_EXCEL,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A8Api.is_response_success(response):
            paging = response.get("data").get("meta")
            if int(paging.get("last_page")) < page:
                return None
            return response.get("data").get("response")
        return None

    @staticmethod
    def upload(params, files):
        query_params = dict(
            user_id=params.get("user_id"),
            filename=params.get("filename"),
        )
        query_params = A8Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A8_DOCUMENT,
            version=SERVICE_VERSION_A8_DOCUMENT,
            action=SERVICE_ACTION_A8_UPLOAD,
        )

        response = request_dao.upload(url, params=query_params, files=files)
        if A8Api.is_response_success(response):
            return response.get("data")
        return None

    @staticmethod
    def download(params):
        query_params = dict(
            name=params.get("name"),
        )
        query_params = A8Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=SERVICE_DOMAIN_A8_DOCUMENT,
            version=SERVICE_VERSION_A8_DOCUMENT,
            action=SERVICE_ACTION_A8_DOWNLOAD,
        )

        response = request_dao.get(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        if A8Api.is_response_success(response):
            return response.get("data")
        return None
