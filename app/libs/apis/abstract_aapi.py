#!/usr/bin/python
# -*- coding: utf-8 -*-
import time
import uuid
import logging

from app.const import SERVICE_ID

logger = logging.getLogger(__name__)


class AbstractAApi:
    @staticmethod
    def is_response_success(response):
        if (
            response is not None
            and type(response) is dict
            and "success" in response
            and response.get("success") == True
        ):
            return True
        return False

    @staticmethod
    def add_query_params(params):
        current_unixtimestamp = int(time.time())
        uuid_note = uuid.uuid4().node
        query_params = params
        query_params.update(
            _sid=SERVICE_ID,
            _aid=0,
            _rid="{time}{uuid}".format(time=current_unixtimestamp, uuid=uuid_note),
        )
        return query_params

    @staticmethod
    def headers():
        return {"X-Request-Id": "UrBox"}
