#!/usr/bin/python
# -*- coding: utf-8 -*-

from app.const import (
    SERVICE_DOMAIN_URCARD, SERVICE_DOMAIN_URCARD_ACTION_ORDER, SERVICE_DOMAIN_URCARD_ACCESS_TOKEN,
    SERVICE_DOMAIN_URCARD_PROFILE_ID,
)

from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import Abstract<PERSON><PERSON>
from loguru import logger

request_dao = RequestDao()


class UrcardAPI(AbstractAApi):
    @staticmethod
    def save(*args, **kwargs):
        url = "{domain}/{action}".format(
            domain=SERVICE_DOMAIN_URCARD,
            action=SERVICE_DOMAIN_URCARD_ACTION_ORDER,
        )
        body = {
            "request_id": kwargs.get("request_id"),
            "po": kwargs.get("po_code"),
            "orders": kwargs.get("orders"),
            "successful": kwargs.get("successful"),
            "message": kwargs.get("message"),
        }
        headers = {
            "Content-Type": "application/json",
            "x-profile-id": SERVICE_DOMAIN_URCARD_PROFILE_ID,
            "x-access-token": SERVICE_DOMAIN_URCARD_ACCESS_TOKEN,
        }

        logger.info(f'UrcardAPI:callback_order:headers: {headers}')
        try:
            response = request_dao.patch(
                url, params=body, headers=headers
            )
            response = response.text or "Response UC Success"
            logger.info(f'UrcardAPI:callback_order:request: {body},response: {response}')

        except Exception as e:
            logger.info(f'UrcardAPI:callback_order:request_exception:{body},response: {str(e)}')
            response = str(e)
        return {
            "request": body,
            "response": response
        }
