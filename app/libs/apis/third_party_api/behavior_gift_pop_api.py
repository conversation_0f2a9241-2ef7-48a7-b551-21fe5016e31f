# coding: utf8

from loguru import logger
from Crypto.Cipher import AES

import requests
import base64
import json
import re

from app.const import (
    THIRD_PARTY_GIFT_POP_HOST,
    THIRD_PARTY_GIFT_POP_AUTH_KEY,
    THIRD_PARTY_GIFT_POP_DECRYPT_KEY,
    THIRD_PARTY_GIFT_POP_LANG,
    THIRD_PARTY_GIFT_POP_PATH_BUY_CODE,
)
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON>pi
from app.libs.base.utility import Utility
from app.security import UrboxOpenSsl


class BehaviorGiftPopApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "GiftPop"
        self.host = THIRD_PARTY_GIFT_POP_HOST
        self.path_get_code = THIRD_PARTY_GIFT_POP_PATH_BUY_CODE
        self.auth_key = THIRD_PARTY_GIFT_POP_AUTH_KEY
        self.decrypt_key = THIRD_PARTY_GIFT_POP_DECRYPT_KEY
        self.lang = THIRD_PARTY_GIFT_POP_LANG
        self.validate_api_config(
            host=self.host,
            path_get_code=self.path_get_code,
            auth_key=self.auth_key,
            lang=self.lang,
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        if quantity > product_info.get('max_codes_per_call'):
            quantity = product_info.get('max_codes_per_call')
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        transaction_id = self.create_transaction_id("URBOX")
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        body = self.get_body_path_get_code(product_code, transaction_id, quantity)
        headers = self.get_headers(body)
        url = "{host}/{path}".format(host=self.host, path=self.path_get_code)

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )

        try:
            response = requests.post(url, json=body, headers=headers)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=response.json(),
                )
            logger.info(response.text)
            if response and response.status_code == 200:
                response_headers = response.headers
                serializer_data = response.json()
                order_info = serializer_data.get("orderInfo")
                signature = response_headers.get("Signature")

                if self.verify_signature(signature, order_info):
                    expired = Utility.convert_datetime_to_unixtimestamp(
                        order_info.get("expiryDate"), "%Y%m%d"
                    )
                    expired = expired + 86399
                    voucher_list = serializer_data.get("voucherList")
                    if order_info.get("resCode") == "0000" and len(voucher_list) > 0:
                        for item in voucher_list:
                            code = self.format_code(
                                serial=None,
                                pin=None,
                                codex=self.decrypt_codex(item.get("pinNo")),
                                expired=expired,
                            )
                            codes.append(code)
                        codes = self.unique_codex_by_product_parent_id(
                            codes, product_info.get("product_parent_id")
                        )
            else:
                Utility.send_telegram_message_to_group(
                    message=f"GiftPop get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"GiftPop get code Exception: {str(e)}"
            )
            self.after_get_code()
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code, transaction_id)
        return codes, {"transaction_id": transaction_id}

    def get_body_path_get_code(self, product_code, transaction_id, quantity):
        return dict(
            authKey=self.auth_key,
            goodsId=product_code,
            sendLang=self.lang,
            quantity=quantity,
            orderNo=transaction_id,
        )

    def get_headers(self, data):
        return {
            "Content-type": "application/json",
            "Signature": self.generate_signature(data),
        }

    def generate_signature(self, data):
        private_key = open(
            "/src/app/libs/apis/keys/privKeyForGiftPop.pem", "rb"
        ).read()  # TODO: Lấy private key trong DB
        return UrboxOpenSsl.generate_signature(private_key, json.dumps(data))

    def verify_signature(self, signature, data):
        return True
        # data = json.dumps(data, separators=(",", ":"))
        # public_key = open(
        #     "/src/app/libs/apis/keys/pubKeyFromGiftPop.pem", "rb"
        # ).read()  # TODO: Lấy public key trong DB
        # return UrboxOpenSsl.verify_signature(public_key, data, signature)

    def decrypt_codex(self, string):
        try:
            key = bytes(self.decrypt_key, "utf-8")
            encrypted_data = base64.b64decode(string)
            cipher = AES.new(key, AES.MODE_ECB)
            decrypt = cipher.decrypt(encrypted_data).decode("utf-8")
            codex = re.sub("[^A-Za-z0-9]+", "", decrypt)
            return codex
        except Exception as e:
            logger.exception(e)
            return False

    def get_limit_code(self):
        pass
