from app.const import (
    GENERATE_CODE_LENGTH_LIMIT,
    DEFINE_SUPPLIER_7ELEVEN,
    DEFINE_SUPPLIER_HOAYEUTHUONG,
    DEFINE_SUPPLIER_IOMEDIA,
    DEFINE_SUPPLIER_THECOFFEEHOUSE,
    DEFINE_SUPPLIER_J<PERSON>LIBEE,
    DEFINE_SUPPLIER_VMG,
    DEFINE_SUPPLIER_VTC,
    DEFINE_SUPPLIER_GIFT_POP,
    DEFINE_SUPPLIER_EZIN,
    DEFINE_SUPPLIER_SHOPEE,
    DEFINE_SUPPLIER_VIETGUYS, DEFINE_SUPPLIER_GRAB,DEFINE_SUPPLIER_SHOPEE_FOOD, DEFINE_SUPPLIER_VMG_V2
)
from app.libs.apis.third_party_api.behavior_7_eleven_api import Behavior7<PERSON>levenApi
from app.libs.apis.third_party_api.behavior_code_api import <PERSON>havior<PERSON><PERSON><PERSON>pi
from app.libs.apis.third_party_api.behavior_gift_pop_api import <PERSON>ha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.libs.apis.third_party_api.behavior_grab_api import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.libs.apis.third_party_api.behavior_hoa_yeu_thuong_api import BehaviorHoaYeuThuongApi
from app.libs.apis.third_party_api.behavior_io_media_api import BehaviorIoMediaApi
from app.libs.apis.third_party_api.behavior_jollibee_api import BehaviorJollibeeApi
from app.libs.apis.third_party_api.behavior_the_coffeehouse_api import BehaviorTheCoffeeHouseApi
from app.libs.apis.third_party_api.behavior_urbox_api import BehaviorUrboxApi
from app.libs.apis.third_party_api.behavior_vmg_api import BehaviorVmgApi
from app.libs.apis.third_party_api.behavior_vtc_api import BehaviorVTCApi
from app.libs.apis.third_party_api.behavior_ezin_api import BehaviorEzinApi
from app.libs.apis.third_party_api.behavior_shopee_api import BehaviorShopeeApi
from app.libs.apis.third_party_api.behavior_vietguys_api import BehaviorVietGuysApi
from app.libs.apis.third_party_api.behavior_shopee_food_api import BehaviorShopeeFoodApi
from app.libs.apis.third_party_api.behavior_vmg_v2_api import BehaviorVmgApiV2

class ContextCodeApi:
    """
    The Context defines the Code Api.
    """

    def __init__(self, behavior_code_api: BehaviorCodeApi):
        self.behavior_code_api = behavior_code_api

    def set_behavior_get_code(self, behavior_code_api: BehaviorCodeApi):
        self.behavior_code_api = behavior_code_api

    def get_behavior_get_code(self):
        return self.behavior_code_api

    def get_code(self, product_info):
        input_get_code = self.__transform_input(product_info)
        return self.behavior_code_api.get_code(input_get_code)
    def __transform_input(self, input):
        return dict(
            product_id=int(input.get("product_id") or 0),
            supplier_id=int(input.get("supplier_id") or 0),
            product_supplier_id=int(input.get("product_supplier_id") or 0),
            code_prefix=str(input.get("code_prefix") or "UB"),
            code_length=int(input.get("code_length") or GENERATE_CODE_LENGTH_LIMIT),
            scheme_code=str(input.get("scheme_code") or ""),
            product_code=str(input.get("product_code") or ""),
            po_code=str(input.get("po_code") or ""),
            quantity=int(input.get("quantity") or 0),
            price=int(input.get("price") or 0),
            product_parent_id=int(input.get("product_parent_id") or 0),
            order_id=int(input.get("order_id") or 0),
            effective_date=input.get("effective_date") or None,
            expired=input.get("expired") or None,
            max_codes_per_call=int(input.get("max_codes_per_call") or 100),
            retry_transaction_id=input.get("retry_transaction_id") or '',
            meta_data=input.get("meta_data") or None,
            po_request_id=input.get("po_request_id") or '',
            supplier_order_id=input.get("supplier_order_id") or 0,
            request_round=input.get("request_round") or None,
        )

    @staticmethod
    def get_behavior_by_supplier(supplier):
        behavior = ContextCodeApi.__get_behavior(supplier)
        if not behavior:
            return None
        return behavior

    @staticmethod
    def __get_behavior(supplier):
        if supplier["whoexport"] == 1:
            return None
            # return BehaviorUrboxApi()
        elif supplier["whoexport"] == 2 and supplier["supplier_api_config"] is not None:
            return ContextCodeApi.__get_behavior_api(supplier)
        else:
            return None

    @staticmethod
    def __get_behavior_api(supplier):
        selected = {
            DEFINE_SUPPLIER_7ELEVEN: Behavior7ElevenApi(),
            DEFINE_SUPPLIER_HOAYEUTHUONG: BehaviorHoaYeuThuongApi(),
            DEFINE_SUPPLIER_IOMEDIA: BehaviorIoMediaApi(),
            DEFINE_SUPPLIER_JOLLIBEE: BehaviorJollibeeApi(),
            DEFINE_SUPPLIER_THECOFFEEHOUSE: BehaviorTheCoffeeHouseApi(),
            DEFINE_SUPPLIER_VMG: BehaviorVmgApi(),
            DEFINE_SUPPLIER_VTC: BehaviorVTCApi(),
            DEFINE_SUPPLIER_GIFT_POP: BehaviorGiftPopApi(),
            DEFINE_SUPPLIER_EZIN: BehaviorEzinApi(),
            DEFINE_SUPPLIER_SHOPEE: BehaviorShopeeApi(),
            DEFINE_SUPPLIER_VIETGUYS: BehaviorVietGuysApi(),
            DEFINE_SUPPLIER_GRAB: BehaviorGrabApi(),
            DEFINE_SUPPLIER_SHOPEE_FOOD: BehaviorShopeeFoodApi(),
            DEFINE_SUPPLIER_VMG_V2: BehaviorVmgApiV2(),
        }
        return selected.get(supplier.get("supplier_api_config").get("class_api"))
