# coding: utf8

import requests
import logging

from app.const import (
    THIRD_PARTY_7ELEVEN_HOST,
    THIRD_PARTY_7ELEVEN_TOKEN,
    THIRD_PARTY_7ELEVEN_PATH_GET_CODE,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import BehaviorCodeApi

logger = logging.getLogger(__name__)


class Behavior7ElevenApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "7ElevenApi"
        self.host = THIRD_PARTY_7ELEVEN_HOST
        self.path_get_code = THIRD_PARTY_7ELEVEN_PATH_GET_CODE
        self.token = THIRD_PARTY_7ELEVEN_TOKEN
        self.validate_api_config(
            host=self.host, path_get_code=self.path_get_code, token=self.token
        )

    def get_body_path_get_code(self, product_code, quantity):
        return dict(code=product_code, quantity=quantity)

    def get_headers(self):
        return {"Content-type": "application/json", "Authorization": self.token}

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        headers = self.get_headers()
        body = self.get_body_path_get_code(product_code, quantity)
        url = "{host}{path}".format(host=self.host, path=self.path_get_code)

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )
        try:
            response = requests.post(url, json=body, headers=headers)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=response.json(),
                )
            if response and response.status_code == 201:
                serializer_data = response.json()
                if serializer_data["data"]:
                    for code_item in serializer_data["data"]:
                        expired = Utility.convert_datetime_to_unixtimestamp(
                            code_item.get("expired_at"), "%Y-%m-%dT%H:%M:%S.%f%z"
                        )
                        code = self.format_code(
                            serial=None,
                            pin=None,
                            codex=code_item.get("qr_text"),
                            expired=expired,
                        )
                        codes.append(code)
                    codes = self.unique_codex_by_product_parent_id(
                        codes, product_info.get("product_parent_id")
                    )
            else:
                Utility.send_telegram_message_to_group(
                    message=f"7ElevenApi get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"7ElevenApi get code Exception: {str(e)}"
            )
            self.after_get_code()
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code, transaction_id)
        return codes, {"transaction_id": transaction_id}

    def get_limit_code(self):
        pass
