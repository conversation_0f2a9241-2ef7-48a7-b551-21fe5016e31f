# coding: utf8

from loguru import logger
from requests.auth import HTT<PERSON><PERSON>asicAuth

import requests
import hashlib
import json

from app.const import (
    THIRD_PARTY_HOA_YEU_THUONG_HOST,
    THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_ONE_CODE,
    THIRD_PARTY_HOA_YEU_THUONG_PASSWORD,
    THIRD_PARTY_HOA_YEU_THUONG_USERNAME,
    THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_CODE,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON><PERSON>
from app.security import UrBoxHash, UrBoxCrypt


class BehaviorHoaYeuThuongApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "HoaYeuThuongApi"
        self.host = THIRD_PARTY_HOA_YEU_THUONG_HOST
        self.path_get_code = THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_CODE
        self.path_get_one_code = THIRD_PARTY_HOA_YEU_THUONG_PATH_GET_ONE_CODE
        self.password = THIRD_PARTY_HOA_YEU_THUONG_PASSWORD
        self.username = THIRD_PARTY_HOA_YEU_THUONG_USERNAME

        self.validate_api_config(
            host=self.host,
            path_get_code=self.path_get_code,
            path_get_one_code=self.path_get_one_code,
            password=self.password,
            username=self.username,
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        product_parent_id = product_info.get("product_parent_id")
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        codes = self.get_multiple_code(
            transaction_id, product_code, quantity, product_parent_id
        )
        if len(codes) < 1:
            codes = self.get_one_code(
                transaction_id, product_code, quantity, product_parent_id
            )
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code, transaction_id)
        return codes, {"transaction_id": transaction_id}

    def get_headers(self):
        authorization = "{username}:{password}".format(
            username=self.username, password=UrBoxHash.sha256(self.password)
        )
        base64_authorization = UrBoxCrypt.base64_encode(authorization)
        auth = "Basic {auth}".format(auth=base64_authorization)
        return {"Content-type": "application/json", "Authorization": auth}

    def get_body_path_get_code(self, amount, quantity):
        return dict(Quantity=quantity, Amount=amount)

    def get_multiple_code(
        self, transaction_id, product_code, quantity, product_parent_id
    ):
        codes = []
        headers = self.get_headers()
        body = self.get_body_path_get_code(product_code, quantity)
        url = "{host}{path}".format(host=self.host, path=self.path_get_code)

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )
        try:
            response = requests.post(url, data=json.dumps(body), headers=headers)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=response.json(),
                )
            if response and response.status_code == 200:
                serializer_data = response.json()
                # Sample serializer_data
                # {'Data': [{'Amount': 250000, 'CreateDate': '18/08/2021', 'ExpiryDate': '2021-11-18', 'VoucherCode': 'URBRF3300'}], 'Message': '', 'MessageCode': 0, 'Success': True}
                if (
                    serializer_data["Success"]
                    and serializer_data["MessageCode"] == 0
                    and serializer_data["Data"]
                ):
                    for code_item in serializer_data["Data"]:
                        expired = Utility.convert_datetime_to_unixtimestamp(
                            code_item.get("ExpiryDate"), "%Y-%m-%d"
                        )
                        code = self.format_code(
                            serial=None,
                            pin=None,
                            codex=code_item.get("VoucherCode"),
                            expired=expired,
                        )
                        codes.append(code)
                    codes = self.unique_codex_by_product_parent_id(
                        codes, product_parent_id
                    )
            else:
                Utility.send_telegram_message_to_group(
                    message=f"HoaYeuThuongApi get multiple code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"HoaYeuThuongApi get multiple code Exception: {str(e)}"
            )
            self.after_get_code()
        return codes

    def get_one_code(self, transaction_id, product_code, quantity, product_parent_id):
        codes = []
        url = "{host}{path}/{product_code}".format(
            host=self.host, path=self.path_get_one_code, product_code=product_code
        )
        try:
            for i in range(0, quantity):
                code = self.get_one_code_from_api(transaction_id, url)
                if code:
                    codes.append(code)
            codes = self.unique_codex_by_product_parent_id(codes, product_parent_id)
        except Exception as e:
            logger.exception(e)
        return codes

    def get_one_code_from_api(self, transaction_id, url):
        code = None
        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=[],
            data=[],
        )
        auth = HTTPBasicAuth("urbox", hashlib.sha256(b"URBOX_@MTTEST").hexdigest())
        response = requests.get(url, auth=auth)
        if log is not None:
            self.update_logs(
                log=log,
                headers=response.headers,
                response_code=response.status_code,
                data=response.json(),
            )
        if response and response.status_code == 200:
            serializer_data = response.json()
            # Sample serializer_data
            # {'Data': {'Amount': 250000, 'CreateDate': '04/10/2021', 'ExpiryDate': '2022-01-04', 'VoucherCode': 'URBUI5803'}, 'Message': '', 'MessageCode': 0, 'Success': True}
            if (
                serializer_data["Success"]
                and serializer_data["MessageCode"] == 0
                and serializer_data["Data"]
            ):
                code_info = serializer_data["Data"]
                expired = Utility.convert_datetime_to_unixtimestamp(
                    code_info.get("ExpiryDate"), "%Y-%m-%d"
                )
                code = self.format_code(
                    serial=None,
                    pin=None,
                    codex=code_info.get("VoucherCode"),
                    expired=expired,
                )
        else:
            Utility.send_telegram_message_to_group(
                message=f"HoaYeuThuongApi get one code failed: {response.status_code} - {response.text}"
            )
        return code

    def get_limit_code(self):
        pass
