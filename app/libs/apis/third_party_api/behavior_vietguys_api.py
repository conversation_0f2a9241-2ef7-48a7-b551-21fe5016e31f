# coding: utf8
import json
import os

from loguru import logger

import requests

from app.const import (
    THIRD_PARTY_VIETGUYS_PATH_BUY_CODE,
    THIRD_PARTY_VIETGUYS_PARTNER_USERNAME,
    THIRD_PARTY_VIETGUYS_PATH_AUTHEN,
    THIRD_PARTY_VIETGUYS_SECRET_CODE, PROCESS_SENT, STATUS_ON, PROCESS_FAIL,
    THIRD_PARTY_VIETGUYS_ACCESS_TOKEN
)
from app.extensions import redis_client
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.repositories import order_detail_repo
from app.security import UrboxOpenSsl, VietGuysCipher
from app.helper import Helper


class BehaviorVietGuysApi(AbstractApi, Behavior<PERSON>ode<PERSON><PERSON>):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "VietGuysApi"
        self.path_get_code = THIRD_PARTY_VIETGUYS_PATH_BUY_CODE
        self.path_authen = THIRD_PARTY_VIETGUYS_PATH_AUTHEN
        self.username = THIRD_PARTY_VIETGUYS_PARTNER_USERNAME
        self.secret_code = THIRD_PARTY_VIETGUYS_SECRET_CODE
        self.share_key = ''
        self.access_token = THIRD_PARTY_VIETGUYS_ACCESS_TOKEN

        self.validate_api_config(
            path_get_code=self.path_get_code,
            path_authen=self.path_authen,
            username=self.username,
            secret_code=self.secret_code,
            access_token=self.access_token
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        retry_transaction_id = product_info.get("retry_transaction_id") or None
        if retry_transaction_id is not None and retry_transaction_id != '':
            transaction_id = retry_transaction_id
        else:
            custom_transaction_id = ""
            if product_info.get("po_code") != "" and product_info.get("po_request_id") != "":
                custom_transaction_id = f'{product_info.get("po_request_id")}-{product_info.get("po_code")}-'
            transaction_id = self.before_get_code(custom_transaction_id)

        supplier_order_id = self.create_order_log(
            product_supplier_id = product_info.get("product_supplier_id"),
            transaction_id=transaction_id,
            supplier_id=product_info.get("supplier_id"),
            quantity=quantity,
            product_id=product_id,
            po_code=product_info.get("po_code"),
        )
        body = self.__get_body_path_get_code(product_code, price, quantity, transaction_id)
        error_message = ''
        try:
            headers = self.__get_headers()
            url = "{host}{path}".format(host='', path=self.path_get_code)

            log = self.create_logs(
                transaction_id=transaction_id,
                merchant_name=self.title,
                url=url,
                headers=headers,
                data=body,
            )
            del body["raw_signature"]
            response = requests.post(url, json=body, headers=headers)
            logger.info(response.text)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data = {
                        "response_buy": response.json()
                    }
                )

            order_detail_log = {
                "order_id": product_info.get("order_id") or 0,
                "quantity": quantity or 0,
                "supplier_order_id": supplier_order_id or 0,
                "request": str(body),
                "response": str(response.json()),
                "transaction_id": transaction_id,
                "http_code": response.status_code,
                "request_url": url,
                "header": str(headers)
            }

            if response is None:
                order_detail_log.update({
                    "action": PROCESS_FAIL
                })
                self.save_log(**order_detail_log)
                error_message = 'xảy ra lỗi khi tạo order'
                #raise Exception(f"error: {response.json()}")
            else:
                response_body = response.json()
                if response.status_code == 200:
                    if response_body.get("error") != 0:
                        order_detail_log.update({
                            "action": PROCESS_FAIL
                        })
                        error_message = response_body.get("message") or 'xảy ra lỗi khi lấy thông tin tạo order'
                    if response_body.get("error") == 0 and response_body.get("data"):
                        data = response_body.get("data")
                        transaction_res = VietGuysCipher.decrypt(data, self.secret_code, self.share_key)
                        if transaction_res:
                            transaction = json.loads(transaction_res)
                            logger.info(f'{transaction}')
                            order_detail_log.update({
                                "order_code": transaction.get("transaction_id"),
                                "action": PROCESS_SENT
                            })
                else:
                    order_detail_log.update({
                        "action": PROCESS_FAIL
                    })
                    self.save_log(**order_detail_log)
                    error_message = response_body.get('message') or 'xảy ra lỗi khi lấy thông tin tạo order'
            self.save_log(**order_detail_log)
        except Exception as e:
            logger.exception(e)
            self.after_get_code()
            error_message = str(e)
            # raise InternalServerError(
            #     description=f"{e}"
            # )
            # quantity_success = len(codes)
        # quantity_error = quantity - quantity_success
        #self.update_order_log(order_id, 0, 0)
        # self.creater_order_detail_log(codes, order_id, product_id, price, product_code)
        return codes, {"transaction_id": transaction_id, "message": error_message}

    def __get_body_path_get_code(self, product_code, amount, quantity, transaction_id):
        # transaction_id = "{username}_{uuid}".format(
        #     username=self.username,
        #     uuid=Utility.generate_uuid(),
        # )
        raw_signature = {
            "telco": product_code,
            "tid": transaction_id,
            "quantity": quantity,
            "type": 'card',
            "amount": str(amount),
        }
        signature = self.__generate_signature(raw_signature)

        return dict(
            u=self.username,
            iv=self.secret_code,
            ct=signature,
            raw_signature=raw_signature
        )

    def __get_headers(self):
        token = self.__get_token_v2()
        return {
            "Content-type": "application/json",
            'Access-Token': token
        }

    def generate_shared_key(self):
        private_key = self.__get_private_key()
        public_key = self.__get_public_key()
        share_key = VietGuysCipher.share_key(private_key, public_key)
        return share_key

    def __generate_signature(self, raw_string):
        private_key = self.__get_private_key()
        public_key = self.__get_public_key()
        share_key = VietGuysCipher.share_key(private_key, public_key)
        raw_string = json.dumps(raw_string)
        self.share_key = share_key
        return VietGuysCipher.encrypt(raw_string, self.secret_code, share_key)

    def __verify_signature(self, raw_string, signature):
        public_key = self.__get_public_key()
        return UrboxOpenSsl.verify_signature(public_key, raw_string, signature, True)

    def __decrypt_codex(self, codex):
        private_key = self.__get_private_key()
        decrypt_code = UrboxOpenSsl.rsa_decrypt(private_key, codex)
        return decrypt_code

    def __get_private_key(self):
        return open(os.path.abspath("app/libs/apis/keys/privKeyForVietGuy.pem"), "rb").read()

    def __get_public_key(self):
        return open(os.path.abspath("app/libs/apis/keys/pubKeyFromVietGuys.pem"), "rb").read()

    def get_token(self):
        url = "{host}{path}".format(host='', path=self.path_authen)
        body = {
            "username": self.username,
            "type": "refresh_token"
        }
        access_token = redis_client.get("A12:PARTNER:VIETGUYS:ACCESS_TOKEN")
        if access_token != '' and access_token is not None:
            access_token = access_token.decode('utf-8')
            return access_token

        refresh_token = redis_client.get("A12:PARTNER:VIETGUYS:REFRESH_TOKEN")
        refresh_token = refresh_token.decode('utf-8')
        if refresh_token is None or refresh_token == '':
            raise Exception(f'{self.title}: Không tồn tại refresh token')
        logger.info('refresh_token', refresh_token)
        headers = {
            'Refresh-Token': refresh_token
        }
        try:
            logger.info('start...')
            logger.info(url)
            logger.info(body)
            logger.info(headers)
            response = requests.post(url, data=body, headers=headers)
            logger.info(f'VIETGUYS_GET_TOKEN: {response.json()}')
            if response and response.status_code == 200:
                response_body = response.json()
                if response_body.get("error") == 0 and response_body.get("data"):
                    token_response = response_body.get("data")
                    if token_response.get('access_token') and token_response.get('access_token') != '':
                        redis_client.set("A12:PARTNER:VIETGUYS:ACCESS_TOKEN", token_response.get('access_token'),  3600 * 10)
                        redis_client.set("A12:PARTNER:VIETGUYS:REFRESH_TOKEN", token_response.get('refresh_token'))
                        return token_response.get('access_token')
            raise Exception(f'{self.title}: Không lấy được token!')
        except Exception as e:
            logger.info(str(e))
            logger.exception(e)
            raise Exception(f'{self.title}: Exception không lấy được token')

    def __get_token_v2(self):
        return self.access_token

    def save_log(self, *args, **kwargs):
        order_detail_repo.create(**{
            "order_id": kwargs.get("order_id") or 0,
            "supplier_order_id": kwargs.get("supplier_order_id") or 0,
            "order_code": kwargs.get("order_code") or "",
            "request": kwargs.get("request") or '',
            "response": kwargs.get("response") or '',
            "transaction_id": kwargs.get("transaction_id") or '',
            "callback_body": kwargs.get("callback_body") or '',
            "header": kwargs.get("header") or '',
            "request_url": kwargs.get("request_url") or '',
            "http_code": kwargs.get("http_code") or '',
            "quantity": kwargs.get("quantity") or '',
            "status": kwargs.get("status") or STATUS_ON,
            "action": kwargs.get("action") or PROCESS_SENT,
            "created": Helper.get_now_unix_timestamp(),
            "updated": Helper.get_now_unix_timestamp(),
        })
        return True

    def get_limit_code(self):
        pass
