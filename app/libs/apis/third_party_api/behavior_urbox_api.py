# coding: utf8
import time

from loguru import logger

from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import Abstract<PERSON>pi
from app.libs.apis.third_party_api.behavior_code_api import BehaviorCodeApi


class BehaviorUrboxApi(Abstract<PERSON>pi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        self.title = "Urbox"

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        #product_id = product_info.get("product_id")
        #price = product_info.get("price")
        #product_code = product_info.get("product_code")
        #transaction_id = self.before_get_code()
        # order_id = self.create_order_log(
        #     product_info.get("product_supplier_id"),
        #     transaction_id,
        #     product_info.get("supplier_id"),
        #     quantity,
        #     product_id,
        # )

        list_codex = []
        list_plain_codex = []
        quantity_gotten = 0
        quantity_remain = quantity
        try_count = 0
        try:
            while quantity > quantity_gotten and try_count < 10:
                plain_codes = self.__generate_codes(
                    quantity_remain,
                    product_info.get("code_length"),
                    product_info.get("code_prefix"),
                )
                plain_codes = Utility.get_unique_of_list(plain_codes)
                plain_codes = self.__check_codex_exist(list_plain_codex, plain_codes)
                list_plain_codex += plain_codes
                codes = [
                    self.format_code(serial=None, pin=None, codex=code, expired=0)
                    for code in plain_codes
                ]
                list_unique_codex = self.unique_codex_by_product_parent_id(
                    codes, product_info.get("product_parent_id")
                )
                list_codex += list_unique_codex
                quantity_gotten += len(list_codex)
                quantity_remain -= len(list_codex)
                try_count += 1

            if quantity_remain > 0:
                logger.info("Không lấy đủ!")
        except Exception as e:
            logger.exception(e)

        # quantity_success = len(list_codex)
        # quantity_error = quantity - quantity_success
        # self.update_order_log(order_id, quantity_success, quantity_error)
        # self.creater_order_detail_log(
        #     list_codex, order_id, product_id, price, product_code
        # )
        return list_codex, {"transaction_id": f'URBOX-{time.time()}'}

    def __generate_codes(self, quantity, code_length, prefix="UB"):
        codes = []
        random_codes = Utility.get_random_character(quantity, code_length)
        for random_code in random_codes:
            codex = f"{prefix}{random_code}"
            codes.append(codex)
        return codes

    def __check_codex_exist(self, origin, new):
        list_codex = []
        for item in new:
            if item not in origin:
                list_codex.append(item)
        return list_codex

    def get_limit_code(self):
        pass
