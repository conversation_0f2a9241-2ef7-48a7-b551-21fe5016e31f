# coding: utf8
from datetime import datetime

from app.const import (
    THIRD_PARTY_SHOPEE_FOOD_HOST,
    THIRD_PARTY_SHOPEE_FOOD_PATH_BUY_CODE,
    THIRD_PARTY_SHOPEE_FOOD_APP_ID,
    THIRD_PARTY_SHOPEE_FOOD_APP_SECRET
)
from app.libs.base.utility import Utility
from app.security import <PERSON><PERSON><PERSON>oodHMac
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON>pi
from loguru import logger

import json
import random
import requests
import time
import app.const as app_const


class BehaviorShopeeFoodApi(AbstractApi, <PERSON>havior<PERSON>ode<PERSON>pi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "ShopeeFoodApi"
        self.host = THIRD_PARTY_SHOPEE_FOOD_HOST
        self.path_get_code = THIRD_PARTY_SHOPEE_FOOD_PATH_BUY_CODE
        self.api_id = THIRD_PARTY_SHOPEE_FOOD_APP_ID
        self.app_secret = THIRD_PARTY_SHOPEE_FOOD_APP_SECRET
        self.validate_api_config(
            host=self.host,
            path_get_code=self.path_get_code,
            api_id=self.api_id,
            app_secret=self.app_secret
        )

    def get_code(self, product_info):
        logger.info(product_info)
        self.validate_get_code(product_info)
        if not product_info.get('scheme_code'):
            return [], {"transaction_id": "NO_TRANSACTION_ID", "message": "scheme_code is required and cannot be empty"}
        meta_data = product_info.get('meta_data')
        if meta_data is None or meta_data.get('order_date') is None or meta_data.get('order_date') <= 0:
            return [], {"transaction_id": "NO_TRANSACTION_ID", "message": f"meta_data or order_date is invalid {meta_data}"}
        product_info.update(
            start_date=meta_data.get('order_date') - 3600 * 24 * 1,
            end_date=meta_data.get('order_date') + 3600 * 24 * 10,
        )
        list_code = []

        retry_transaction_id = product_info.get("retry_transaction_id") or ""
        if retry_transaction_id != '':
            transaction_id = retry_transaction_id
        else:
            transaction_id = self.generate_transaction_id()

        supplier_order_id = self.create_order_log(
            product_supplier_id=product_info.get("product_supplier_id"),
            transaction_id=transaction_id,
            supplier_id=product_info.get("supplier_id"),
            quantity=product_info.get("quantity"),
            product_id=product_info.get("product_id"),
            po_code=product_info.get("po_code"),
        )
        body = self.generate_body(
            start_time=product_info.get("start_date"),
            end_time=product_info.get("end_date"),
            scheme_code=product_info.get('scheme_code')
        )
        headers = self.generate_headers(body, transaction_id)
        url = f"{self.host}{self.path_get_code}"

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
            request_round=product_info.get('request_round') or None
        )
        error_message = ''
        order_detail_log = {
            "order_id": product_info.get("order_id") or 0,
            "supplier_order_id": supplier_order_id or 0,
            "request": str(body),
            "transaction_id": transaction_id,
            "request_url": url,
            "quantity": product_info.get("quantity"),
            "header": str(headers)
        }

        try:
            response = requests.post(url, json=body, headers=headers)
            try:
                log_data = response.json()
                order_detail_log.update({
                    "response": "get code success",
                    "http_code": response.status_code,
                })
            except Exception as e:
                logger.error(e)
                log_data = str(e)
                order_detail_log.update({
                    "response": str(log_data),
                    "http_code": response.status_code,
                })
            self.update_logs(
                log=log,
                headers=response.headers,
                response_code=response.status_code,
                data=log_data,
            )
            if response and response.status_code == 200:
                response_data = response.json()
                if response_data.get("result") == "success":
                    voucher_infos = response_data.get("reply").get("voucher_infos")
                    if voucher_infos and len(voucher_infos) > 0:
                        for voucher_info in voucher_infos:
                            expired = Utility.convert_datetime_to_unixtimestamp(
                                voucher_info.get("end_time"), "%Y-%m-%d %H:%M:%S"
                            )
                            list_code.append(self.format_code(
                                serial=None,
                                pin=None,
                                codex=voucher_info.get('promotion_code'),
                                expired=expired
                            ))
                        len_list_code_before = len(list_code)
                        list_code = self.unique_codex_by_product_parent_id(
                            list_code, product_info.get("product_parent_id")
                        )
                        len_list_code_after = len(list_code)
                        if len_list_code_after != len_list_code_before:
                            Utility.send_telegram_message_to_group(
                                f"Product {product_info.get('product_id')} - Order: {supplier_order_id}. Code lấy được đã trùng với code trung kho. Trùng {len_list_code_before - len_list_code_after}/{len_list_code_before} code")

                        order_detail_log.update({
                            "action": app_const.PROCESS_SUCCESS
                        })
                else:
                    error_message = response_data.get("result")
                    logger.error(response.text)
                    order_detail_log.update({
                        "action": app_const.PROCESS_FAIL
                    })

            else:
                error_message = response.text
                order_detail_log.update({
                    "action": app_const.PROCESS_FAIL
                })
        except Exception as e:
            logger.exception(e)
            error_message = str(e)
            order_detail_log.update({
                "action": app_const.PROCESS_FAIL
            })
        self.save_order_detail_log(**order_detail_log)
        quantity_success = len(list_code)
        quantity_error = product_info.get("quantity") - quantity_success
        self.update_order_log(supplier_order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            codes=list_code,
            order_id=supplier_order_id,
            product_id=product_info.get("product_id"),
            money=product_info.get("price"),
            product_code=product_info.get("product_code")
        )
        codes = list_code
        return codes, {"transaction_id": transaction_id, "message": error_message}

    def generate_transaction_id(self):
        random_string = random.randint(1000000, 9999999)
        return f"{int(time.time())}{random_string}"

    def generate_body(self, start_time, end_time, scheme_code):
        start_time = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d %H:%M:%S")
        end_time = datetime.fromtimestamp(end_time).strftime("%Y-%m-%d %H:%M:%S")
        body = {
            "start_time": start_time,
            'end_time': end_time,
            "note": scheme_code
        }

        return body

    def generate_headers(self, body, transaction_id):
        return {
            "Content-type": "application/json",
            "Authorization": f'Signature {self.generate_sign(body)}',
            "X-Foody-App-Id": self.api_id,
            "X-Foody-Api-Version": "1",  # current version of foody
            "X-Foody-Request-Id": transaction_id,
            "X-Foody-Country": "VN",
            "X-Foody-Language": "vi"
        }

    def generate_sign(self, body):
        url = f"{self.host}{self.path_get_code}"
        body_string = json.dumps(body, ensure_ascii=False)
        string = f'POST|{url}|{body_string}'
        sign = ShopeeFoodHMac.hmac_sha256(self.app_secret, string)
        return sign

    def get_limit_code(self):
        pass
