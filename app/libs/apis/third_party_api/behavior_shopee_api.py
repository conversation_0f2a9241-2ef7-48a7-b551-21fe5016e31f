# coding: utf8

from app.const import (
    THIRD_PARTY_SHOPEE_HOST,
    THIRD_PARTY_SHOPEE_PATH_BUY_CODE,
    THIRD_PARTY_SHOPEE_PARTNER_CODE,
    THIRD_PARTY_SHOPEE_PARTNER_SECRET, THIRD_PARTY_SHOPEE_PRIVKEYFORSHOPEE_PEM
)
from app.security import <PERSON>eeAESCip<PERSON>, UrBoxRsa
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON><PERSON>
from loguru import logger

import json
import random
import requests
import time

from app.utils import set_rate_limit


class BehaviorShopeeApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "ShopeeApi"
        self.host = THIRD_PARTY_SHOPEE_HOST
        self.path_get_code = THIRD_PARTY_SHOPEE_PATH_BUY_CODE
        self.partner_code = THIRD_PARTY_SHOPEE_PARTNER_CODE
        self.partner_secret = THIRD_PARTY_SHOPEE_PARTNER_SECRET
        self.RATE_LIMIT = 15
        self.validate_api_config(
            host=self.host,
            path_get_code=self.path_get_code,
            partner_code=self.partner_code,
            partner_secret=self.partner_secret
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        set_rate_limit(product_info.get("product_id"), self.RATE_LIMIT)
        list_code = []
        transaction_id = self.generate_transaction_id()
        retry_transaction_id = product_info.get("retry_transaction_id") or ""
        if retry_transaction_id != '':
            transaction_id = retry_transaction_id
        supplier_order_id = self.create_order_log(
            product_supplier_id=product_info.get("product_supplier_id"),
            transaction_id=transaction_id,
            supplier_id=product_info.get("supplier_id"),
            quantity=product_info.get("quantity"),
            product_id=product_info.get("product_id"),
            po_code=product_info.get("po_code"),
        )
        body = self.generate_body(
            reference=transaction_id,
            scheme_code=product_info.get("scheme_code"),
            quantity=product_info.get("quantity"),
            discount_value=product_info.get("price")
        )
        headers = self.generate_headers(body)
        url = f"{self.host}/{self.path_get_code}/"

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )
        error_message = ''
        try:
            logger.info(f'body get code: {body}')
            response = requests.post(url, json=body, headers=headers)
            try:
                log_data = response.json()
            except:
                log_data = response.content.decode()

            self.update_logs(
                log=log,
                headers=response.headers,
                response_code=response.status_code,
                data=log_data,
            )
            if response and response.status_code == 200:
                response_data = response.json()
                if response_data.get("code") == 0:
                    data = response_data.get("data").get("voucher_list")
                    if data and len(data) > 0:
                        list_voucher = data[0]
                        expired = list_voucher.get("valid_to")
                        for response_code in list_voucher.get("vouchers"):
                            list_code.append(self.format_code(
                                serial=None,
                                pin=None,
                                codex=ShopeeAESCipher.decrypt(response_code, self.partner_secret),
                                expired=expired,
                            ))
                        list_code = self.unique_codex_by_product_parent_id(
                            list_code, product_info.get("product_parent_id")
                        )
                else:
                    error_message = response_data.get("message")
            else:
                logger.info(response.content.decode())
                error_message = f'{response.status_code} - Có lỗi xảy ra khi get code shopee'
        except Exception as e:
            logger.exception(e)
            error_message = str(e)

        quantity_success = len(list_code)
        quantity_error = product_info.get("quantity") - quantity_success
        self.update_order_log(supplier_order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            codes=list_code,
            order_id=supplier_order_id,
            product_id=product_info.get("product_id"),
            money=product_info.get("price"),
            product_code=product_info.get("scheme_code"),
            get_transaction_id=transaction_id,
            response_transaction_id=product_info.get("scheme_code"),
            po_code=product_info.get("po_code")
        )
        codes = list_code
        return codes, {"transaction_id": transaction_id, "message": error_message}

    def generate_transaction_id(self):
        random_string = random.randint(1000000, 9999999)
        return f"UB{int(time.time())}{random_string}"

    def generate_body(self, reference, scheme_code, quantity, discount_value):
        body = {
            "partner_code": self.partner_code,
            "reference": reference,
            "quantity": quantity,
            "discount_value": discount_value
        }

        if scheme_code:
            body.update({"scheme_code": scheme_code})
        return body

    def generate_headers(self, body):
        return {"Content-type": "application/json", "signature": self.generate_sign(body)}

    def generate_sign(self, body):
        private_key = THIRD_PARTY_SHOPEE_PRIVKEYFORSHOPEE_PEM
        return UrBoxRsa.sha1_sign(json.dumps(body, sort_keys=True, separators=(',', ':')), private_key)

    def get_limit_code(self):
        pass
