# coding: utf8
from loguru import logger

import datetime
import requests
import json

from app.const import (
    THIRD_PARTY_VTC_TRIPLE_DES_KEY,
    THIRD_PARTY_VTC_PARTNER_CODE,
    THIRD_PARTY_VTC_PATH_BUY_CARD,
    THIRD_PARTY_VTC_BUY_CARD_CATEGORY,
    THIRD_PARTY_VTC_HOST,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON><PERSON>
from app.security import UrboxTripleDes, UrboxOpenSsl


class BehaviorVTCApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "VTCApi"
        self.host = THIRD_PARTY_VTC_HOST
        self.path_buy_card = THIRD_PARTY_VTC_PATH_BUY_CARD
        self.partner_code = THIRD_PARTY_VTC_PARTNER_CODE
        self.buy_card_category = THIRD_PARTY_VTC_BUY_CARD_CATEGORY
        self.triple_des_key = THIRD_PARTY_VTC_TRIPLE_DES_KEY

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        try:
            product_code = product_code.split(";")
            vtc_category_id = product_code[0]
            vtc_product_id = product_code[1]
            response = self.__get_code_from_api(transaction_id, vtc_category_id, vtc_product_id, price, quantity)
            if response and response.status_code == 200:
                serializer_data = response.json()
                if (
                    self.__verify_signature(serializer_data) is True
                    and serializer_data.get("responseCode") == 1
                ):
                    data_info = UrboxTripleDes.decode(
                        serializer_data.get("dataInfo"), self.triple_des_key, False
                    )
                    list_codes = json.loads(data_info)
                    for code_item in list_codes.get("ListCard"):
                        expired = Utility.convert_datetime_to_unixtimestamp(
                            code_item.get("ExpriredDate"), "%Y-%m-%dT%H:%M:%S"
                        )
                        code = self.format_code(
                            serial=code_item.get("Serial"),
                            pin=None,
                            codex=code_item.get("Code"),
                            expired=expired,
                        )
                        codes.append(code)
                    codes = self.unique_codex_by_product_parent_id(
                        codes, product_info.get("product_parent_id")
                    )
                    # TODO: save log
            else:
                Utility.send_telegram_message_to_group(
                    message=f"VTCApi get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"VTCApi get code Exception: {str(e)}"
            )
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code,transaction_id)
        return codes, {"transaction_id": transaction_id}

    def __generate_signature(self, category_id, product_id, amount, transaction_id, trans_date, data):
        string = f"{self.partner_code}|{category_id}|{product_id}|{amount}||{transaction_id}|{trans_date}|{data}"
        private_key = open(
            "/src/app/libs/apis/keys/privKeyForVTC.pem", "rb"
        ).read()  # TODO: Lấy private key trong DB
        return UrboxOpenSsl.generate_signature(private_key, string)

    def __verify_signature(self, data):
        return True
        # string = "{response_code}|{status}|{transaction_id}|{description}|{data_info}".format(
        #     response_code=data.get("responseCode"),
        #     status=data.get("status"),
        #     transaction_id=data.get("partnerTransID"),
        #     description=data.get("description"),
        #     data_info=data.get("dataInfo"),
        # )
        # public_key = open(
        #     "/src/app/libs/apis/keys/pubKeyFromVTC.pem", "rb"
        # ).read()  # TODO: Lấy public key trong DB
        # return UrboxOpenSsl.verify_signature(public_key, string, data.get("dataSign"))

    def __get_headers(self):
        return {"Content-type": "application/json"}

    def __get_body(self, category_id, product_id, amount, transaction_id, trans_date, data, signature):
        return {
            "partnerCode": str(self.partner_code),
            "categoryID": str(category_id),
            "productID": str(product_id),
            "productAmount": str(amount),
            "customerID": "",
            "partnerTransID": str(transaction_id),
            "partnerTransDate": str(trans_date),
            "data": str(data),
            "dataSign": str(signature),
        }

    def __get_code_from_api(self, transaction_id, category_id, product_id, price, quantity):
        trans_date = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        headers = self.__get_headers()
        signature = self.__generate_signature(category_id, product_id, price, transaction_id, trans_date, quantity)
        body = self.__get_body(category_id, product_id, price, transaction_id, trans_date, quantity, signature)
        url = f"{self.host}{self.path_buy_card}"
        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )
        response = requests.post(url, json=body, headers=headers)
        self.update_logs(
            log=log,
            headers=response.headers,
            response_code=response.status_code,
            data=response.json(),
        )
        return response

    def get_limit_code(self):
        pass
