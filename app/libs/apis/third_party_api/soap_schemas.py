# coding: utf8
from zeep.cache import SqliteCache
from zeep.settings import Settings
from zeep.transports import Transport
from requests import Session
import os
import tempfile
import urllib3

# Tắt cảnh báo InsecureRequestWarning
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Tạo thư mục cache tạm thời
cache_dir = os.path.join(tempfile.gettempdir(), 'zeep_cache')
os.makedirs(cache_dir, exist_ok=True)

# Tạo transport với cache
def create_soap_transport(timeout=30, operation_timeout=20):
    """
    Tạo transport tùy chỉnh cho Zeep để bỏ qua lỗi khi tải schema

    Args:
        timeout: Thời gian chờ kết nối (giây)
        operation_timeout: Thời gian chờ hoàn thành thao tác (giây)

    Returns:
        Transport: Transport đã được cấu hình
    """
    session = Session()
    session.verify = False

    # Tạo cache để lưu trữ các schema đã tải
    cache = SqliteCache(path=os.path.join(cache_dir, 'zeep_sqlite.db'))

    # Tạo transport với cache và timeout
    transport = Transport(
        cache=cache,
        session=session,
        timeout=timeout,
        operation_timeout=operation_timeout
    )

    return transport

# Tạo settings với strict=False để bỏ qua lỗi schema
def create_soap_settings():
    """
    Tạo settings tùy chỉnh cho Zeep để bỏ qua lỗi schema

    Returns:
        Settings: Settings đã được cấu hình
    """
    return Settings(strict=False, xml_huge_tree=True)
