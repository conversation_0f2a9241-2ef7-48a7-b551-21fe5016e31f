# coding: utf8

import datetime
import requests
import logging

from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from app.const import (
    THIRD_PARTY_JOLLIBEE_PATH_DELETE,
    THIRD_PARTY_JOLLIBEE_HOST,
    THIRD_PARTY_JOLLIBEE_API_KEY,
    THIRD_PARTY_JOLLIBEE_PATH_GET_CODE,
    THIRD_PARTY_JOLLIBEE_SECRET_CODE,
    THIRD_PARTY_JOLLIBEE_AUTHOR_CODE,
    JOLLIBEE_EXPIRED_DAYS,
)
from app.helper import DatetimeDao
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import BehaviorCodeApi

logger = logging.getLogger(__name__)


class BehaviorJollibeeApi(AbstractApi, Behavior<PERSON>ode<PERSON><PERSON>):
    def __init__(self, *args, **kwargs):
        self.title = "Jollibee"
        self.author_code = THIRD_PARTY_JOLLIBEE_AUTHOR_CODE
        self.secret_code = THIRD_PARTY_JOLLIBEE_SECRET_CODE
        self.api_key = THIRD_PARTY_JOLLIBEE_API_KEY
        self.host = THIRD_PARTY_JOLLIBEE_HOST
        self.path_get_code = THIRD_PARTY_JOLLIBEE_PATH_GET_CODE
        self.path_delete_code = THIRD_PARTY_JOLLIBEE_PATH_DELETE
        self.validate_api_config(
            Host=self.host,
            PathGetCode=self.path_get_code,
            PathDelCode=self.path_delete_code,
            AuthorCode=self.author_code,
            SecretCode=self.secret_code,
            ApiKey=self.api_key,
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        transaction_id = self.create_transaction_id()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        body = self.get_body_path_get_code(product_code, quantity)
        headers = self.get_headers()
        authors = self.get_authors()
        url = "{host}{path}".format(host=self.host, path=self.path_get_code)

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
        )
        try:
            response = requests.get(url, params=body, headers=headers, auth=authors)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=response.json(),
                )

            if response and response.status_code == 200:
                content = response.json()
                if type(content) == list and len(content) > 0:
                    for code in content:
                        if "status" in code and code["status"] == 0:
                            expired = Utility.convert_datetime_to_unixtimestamp(
                                code.get("enddate"), "%Y-%m-%dT%H:%M:%S"
                            )
                            code = self.format_code(
                                serial=None,
                                pin=None,
                                codex=code["number"],
                                expired=expired,
                            )
                            codes.append(code)
                    codes = self.unique_codex_by_product_parent_id(
                        codes, product_info.get("product_parent_id")
                    )
                    # TODO: save log
            else:
                Utility.send_telegram_message_to_group(
                    message=f"Jollibee get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"Jollibee get code Exception: {str(e)}"
            )
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code,transaction_id)
        return codes, {"transaction_id": transaction_id}

    def get_body_path_get_code(self, product_code, quantity):
        effective_from = datetime.datetime.fromtimestamp(
            DatetimeDao.get_now_unixtimestamp()
        )
        effective_to = datetime.datetime.fromtimestamp(
            DatetimeDao.add_day_from_now(JOLLIBEE_EXPIRED_DAYS)
        )
        return dict(
            IsMode=1,
            Quantity=quantity,
            Amount=product_code,
            EffectiveFrom="{year}{month:02d}{day:02d}".format(
                year=effective_from.year,
                month=effective_from.month,
                day=effective_from.day,
            ),
            EffectiveTo="{year}{month:02d}{day:02d}".format(
                year=effective_to.year, month=effective_to.month, day=effective_to.day
            ),
        )

    def get_headers(self):
        return {"Content-type": "application/json", "X-ApiKey": self.api_key}

    def get_authors(self):
        return HTTPBasicAuth(self.author_code, self.secret_code)

    def remove_code(self, codes):
        headers = self.get_headers()
        authors = self.get_authors()
        url = "{host}{path}".format(
            host=self.host,
            path=self.path_delete_code,
        )

        for code in codes:
            try:
                body = self.get_body_path_remove_code(code)
                response = requests.get(url, params=body, headers=headers, auth=authors)
                if response and response.status_code == 200:
                    content = response.json()
                    if type(content) == list and len(content) > 0:
                        pass
                        # TODO: true
            except Exception as e:
                # TODO: false
                logger.exception(e)
        return codes

    def get_body_path_remove_code(self, code):
        return dict(VoucherCode=code)

    def get_limit_code(self):
        pass
