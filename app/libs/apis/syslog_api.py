#!/usr/bin/python
# -*- coding: utf-8 -*-
from urbox_lib.const import SUPPLIER_CONTACT_EMAIL

from app.const import (
    SERVICE_DOMAIN_SYSLOG_URBOX, SERVICE_DOMAIN_SYSLOG_URBOX_ACTION, SERVICE_SYSTEM_LOG_SERVICE_SECRET_KEY,
)
from app.helper import Helper
from app.libs.base.utility import Utility
from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import AbstractAApi

request_dao = RequestDao()


class SyslogApi(AbstractAApi):
    @staticmethod
    def save(*args, **kwargs):
        url = "{domain}/{action}".format(
            domain=SERVICE_DOMAIN_SYSLOG_URBOX,
            action=SERVICE_DOMAIN_SYSLOG_URBOX_ACTION,
        )
        secret_key = SERVICE_SYSTEM_LOG_SERVICE_SECRET_KEY
        body = {
            "log_id": kwargs.get("log_id"),
            "group_id": kwargs.get("group"),
            "request_url": kwargs.get("request_url") or '',
            "request_header": kwargs.get("request_url") or '',
            "response": kwargs.get("response") or '',
            "method": kwargs.get("method"),
            "request_body": kwargs.get("request_body") or '',
            "created": Helper.get_now_unix_timestamp(),
            "updated": Helper.get_now_unix_timestamp(),
            "note": kwargs.get("order_id"),
            "service": "a12-webhook",
        }
        headers = {
            "content-type": "application/json",
            "Secret-Key": secret_key,
        }

        response = request_dao.post(
            url, params=body, headers=headers
        )
        if response is None:
            return False
        return True
