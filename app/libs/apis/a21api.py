#!/usr/bin/python
# -*- coding: utf-8 -*-

import time
import uuid

from loguru import logger

from app.const import (
    ROOT_URL_A21,
    A21_CREATE_SUPPLIER_ORDER,
    SERVICE_VERSION_A21,
    A21_UPDATE_SUPPLIER_ORDER,
    A21_CREATE_MULTI_ORDER_DETAIL,
    SERVICE_ID,
    A21_UPDATE_PROCESS_SUPPLIER_ORDER,
    A21_CREATE_SUPPLIER_ORDER_DETAIL, A21_GET_SUPPLIER_ORDER,
)
from app.libs.base.utility import Utility
from app.libs.apis.abstract_aapi import AbstractAApi
from app.libs.base.request_dao import RequestDao

request_dao = RequestDao()


class A21Api(AbstractAApi):
    @staticmethod
    def create_order_log(*args, **kwargs):
        params = dict(
            product_supplier_id=kwargs.get("product_supplier_id") or None,
            transaction_id=kwargs.get("transaction_id") or None,
            supplier_id=kwargs.get("supplier_id") or None,
            process=1,
            quantity=kwargs.get("quantity") or 0,
            product_id=kwargs.get("product_id") or None,
            po=kwargs.get("po_code") or None,
        )
        params = A21Api.add_query_params(params)
        url = "{domain}/{version}/{action}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_CREATE_SUPPLIER_ORDER,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        logger.info(f'A21:create_order_log:response: {response}')
        if A21Api.is_response_success(response):
            return response.get("data").get("id")
        return False

    @staticmethod
    def update_order_log(*args, **kwargs):
        params = dict(
            id=kwargs.get("id") or None,
            quantity_success=int(kwargs.get("quantity_success")) or 0,
            quantity_error=int(kwargs.get("quantity_error")) or 0,
            process=kwargs.get("process") or 4,
        )

        params = A21Api.add_query_params(params)

        url = "{domain}/{version}/{action}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_UPDATE_SUPPLIER_ORDER,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        logger.info(f'A21:update_order_log:response: {response}')
        return A21Api.is_response_success(response)

    @staticmethod
    def create_order_detail_log(data):
        # TODO: Tạm thời đặt _sid, _aid, _rid trên url, chờ a Thắng fix
        current_unixtimestamp = int(time.time())
        uuid_note = uuid.uuid4().node
        rid = "{time}{uuid}".format(time=current_unixtimestamp, uuid=uuid_note)
        query_params = "_rid={rid}&_aid={aid}&_sid={sid}".format(
            sid=SERVICE_ID, aid=0, rid=rid
        )

        params = A21Api.add_query_params(dict(data=data))
        url = "{domain}/{version}/{action}?{query}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_CREATE_MULTI_ORDER_DETAIL,
            query=query_params,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        logger.info(f'A21:create_order_detail_log:response: {response}')
        return A21Api.is_response_success(response)

    @staticmethod
    def update_process_supplier_order(*args, **kwargs):
        query_params = dict(
            process=kwargs.get("process"),
            id=kwargs.get("id"),
        )
        query_params = A21Api.add_query_params(query_params)
        url = "{domain}/{version}/{action}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_UPDATE_PROCESS_SUPPLIER_ORDER,
        )
        response = request_dao.post(
            url,
            params=query_params,
            headers={"X-Request-Id": Utility.my_random_string()},
        )
        return response

    def create_supplier_order_detail(*args, **kwargs):
        # TODO: Tạm thời đặt _sid, _aid, _rid trên url, chờ a Thắng fix

        params = dict(
            codex=kwargs.get("codex"),
            serial=kwargs.get("serial"),
            pin=kwargs.get("pin"),
            expired=kwargs.get("expired_time"),
            process=kwargs.get("process"),
            supplier_order_id=kwargs.get("supplier_order_id"),
            note=kwargs.get("note"),
            product_code=kwargs.get("product_code"),
            money=kwargs.get("money"),
        )
        params = A21Api.add_query_params(params)

        url = "{domain}/{version}/{action}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_CREATE_SUPPLIER_ORDER_DETAIL,
        )
        response = request_dao.post(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )

        return A21Api.is_response_success(response)

    @staticmethod
    def get_supplier_order(transaction_id):

        params = A21Api.add_query_params({})

        url = "{domain}/{version}/{action}/{transaction_id}".format(
            domain=ROOT_URL_A21,
            version=SERVICE_VERSION_A21,
            action=A21_GET_SUPPLIER_ORDER,
            transaction_id=transaction_id
        )

        response = request_dao.get(
            url, params=params, headers={"X-Request-Id": Utility.my_random_string()}
        )
        if response is not None and type(response) is dict and "data" in response and "id" in response.get("data"):
            return response.get('data')
        return False