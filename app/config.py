#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import urllib.parse

from loguru import logger

"""
Config Redis

REDIS_HOST: Host of redis
REDIS_PORT: Port of redis
REDIS_DB  : Database name of redis

Environment Variable:
    - REDIS_HOST
    - REDIS_PORT
    - REDIS_DB
"""


class ConfigRedis(object):
    REDIS_HOST = os.environ.get("REDIS_HOST") or "localhost"
    REDIS_PORT = os.environ.get("REDIS_PORT") or "6379"
    REDIS_DB = os.environ.get("REDIS_DB") or "6"


"""
Config Kafka

KAFKA_URL: Url to connect to kafka
KAFKA_GROUP: Group Kafka conenct
INSTANCE_TOPICS: Topics to work

Environment Variable:
    - APP_CONFIG: Differentiate environment in kafka
    - KAFKA_URL
"""


class ConfigKafka(object):
    PREFIX = (os.environ.get("APP_CONFIG") or "UNKNOWN").upper()
    KAFKA_URL = os.environ.get("KAFKA_URL") or "urbox-hn-kafka-dev"
    KAFKA_GROUP = f"{PREFIX}_GROUP_A6"
    TOPIC_CHANGE_QUANTITY = f"{PREFIX}_A6_CHANGE_QUANTITY"
    TOPIC_GENERATE_CODE = f"{PREFIX}_A12_GENERATE_CODE"
    TOPIC_SYNCHRONIZE_REDIS = f"{PREFIX}_A12_SYNCHRONIZE_REDIS"
    TOPIC_CODEX_TO_REDIS = f"{PREFIX}_A12_CODEX_TO_REDIS"
    TOPIC_CODEX_TO_REDIS_NEW = f"{PREFIX}_A12_CODEX_TO_REDIS_NEW"
    TOPIC_PO_ORDER_CODE = f"{PREFIX}_A12_PO_ORDER_CODE"
    TOPIC_BEFORE_PO_ORDER_CODE = f"{PREFIX}_A12_BEFORE_PO_ORDER_CODE"
    TOPIC_INSERT_CODEX_DATA = f"{PREFIX}_A12_INSERT_CODEX_DATA"
    INSTANCE_TOPICS = [
        TOPIC_GENERATE_CODE,
        TOPIC_PO_ORDER_CODE,
        TOPIC_BEFORE_PO_ORDER_CODE,
        TOPIC_INSERT_CODEX_DATA
    ]


"""
Config MySQL

SQLALCHEMY_ECHO: Show query mysql for the debug
DB_TYPE: mysql (default)
DB_CONNECTOR: pymysql (default)
DB_USERNAME: Username to connect to database
DB_PASSWORD: Password to connect to database
DB_HOST: Host of database
DB_Port: Port of database
DB_NAME: Database name of project
DATABASE_URL: {db_type}+{db_connector}://{username}:{password}@{host}:{port}/{database}

Environment Variable:
    - DB_MYSQL_TYPE
    - DB_MYSQL_CONNECTOR
    - DB_MYSQL_USER
    - DB_MYSQL_PASS
    - DB_MYSQL_HOST
    - DB_MYSQL_PORT
    - DB_MYSQL_DBNAME
"""


class ConfigMysql(object):
    SQLALCHEMY_ECHO = False
    DB_TYPE = os.environ.get("DB_MYSQL_TYPE") or "mysql"
    DB_CONNECTOR = os.environ.get("DB_MYSQL_CONNECTOR") or "pymysql"
    DB_USERNAME = os.environ.get("DB_MYSQL_USER")
    DB_PASSWORD = os.environ.get("DB_MYSQL_PASS")
    DB_HOST = os.environ.get("DB_MYSQL_HOST")
    DB_PORT = os.environ.get("DB_MYSQL_PORT")
    DB_NAME = os.environ.get("DB_MYSQL_DBNAME")
    DATABASE_URL = f"{DB_TYPE}+{DB_CONNECTOR}://{urllib.parse.quote(str(DB_USERNAME))}:{urllib.parse.quote(str(DB_PASSWORD))}@{DB_HOST}:{DB_PORT}/{DB_NAME}"


"""
Config MongoDB

MONGODB_HOST:
MONGODB_PORT:
MONGODB_DB:
MONGODB_USERNAME:
MONGODB_PASSWORD: 
MONGODB_REPLICASET:
MONGODB_READ_PREFERENCE:
MONGODB_RETRY_WRITES: false (default)

Environment Variable:
    - DB_MONGO_HOST
    - DB_MONGO_PORT
    - DB_MONGO_DATABASE
    - DB_MONGO_USERNAME
    - DB_MONGO_PASSWORD
    - DB_MONGODB_REPLICASET
    - MONGODB_READ_PREFERENCE: secondaryPreferred (default)

Url Builder:
 - {mongo_url}{mongo_username}:{mongo_password}@{mongo_host}:{mongo_port}/{mongo_db}?retryWrites=false&readPreference={read_preference}&replicaSet={mongo_replicaset}
"""


class ConfigMongodb(object):
    # config
    MONGODB_HOST = os.environ.get("DB_MONGO_HOST") or "<your mongodb host>"
    MONGODB_PORT = int(os.environ.get("DB_MONGO_PORT") or "27017")
    MONGODB_DB = os.environ.get("DB_MONGO_DATABASE") or "<your mongodb database>"
    MONGODB_USERNAME = os.environ.get("DB_MONGO_USERNAME") or None
    MONGODB_PASSWORD = os.environ.get("DB_MONGO_PASSWORD") or None
    MONGODB_REPLICASET = os.environ.get("DB_MONGODB_REPLICASET") or None  # 'rs0'
    MONGODB_READ_PREFERENCE = (
        os.environ.get("MONGODB_READ_PREFERENCE") or "secondaryPreferred"
    )
    MONGODB_RETRY_WRITES = "false"

    # logic
    MONGODB_URL = f"mongodb://"
    if MONGODB_USERNAME is not None and MONGODB_PASSWORD is not None:
        MONGODB_URL = f"{MONGODB_URL}{urllib.parse.quote(MONGODB_USERNAME)}:{urllib.parse.quote(MONGODB_PASSWORD)}@{MONGODB_HOST}:{MONGODB_PORT}/"
    else:
        MONGODB_URL = f"{MONGODB_URL}{MONGODB_HOST}:{MONGODB_PORT}/"

    if MONGODB_DB is not None:
        MONGODB_URL = f"{MONGODB_URL}{MONGODB_DB}"
    MONGODB_URL = f"{MONGODB_URL}?retryWrites={MONGODB_RETRY_WRITES}"
    if MONGODB_READ_PREFERENCE is not None:
        MONGODB_URL = f"{MONGODB_URL}&readPreference={MONGODB_READ_PREFERENCE}"
    if MONGODB_REPLICASET is not None:
        MONGODB_URL = f"{MONGODB_URL}&replicaSet={MONGODB_REPLICASET}"


"""
Config Backend API

HOST:
PATH_COMMON_LOG:

Environment Variable:
    - ROOT_URL_BACKEND
    - PATH_COMMON_LOG_BACKEND
"""


class ConfigSystemLogService(object):
    HOST = os.environ.get("ROOT_URL_BACKEND") or "https://dev.urbox.dev"
    PATH_COMMON_LOG = os.environ.get("PATH_COMMON_LOG_BACKEND") or "syslog/common-log"
    TIME_HOLD_CODE_EXPIRED = 20
    SECRET_KEY = os.environ.get("SYSTEM_LOG_SERVICE_SECRET_KEY") or None


class Config(ConfigMysql, ConfigMongodb, ConfigRedis, ConfigKafka):
    SECRET_KEY = os.environ.get("SECRET_KEY") or "<your secret key>"
    VIETTEL_PLUS_TOKEN = os.environ.get("VIETTEL_PLUS_TOKEN") or None
    VIETTEL_PLUS_API_URL = os.environ.get("VIETTEL_PLUS_API_URL") or None
    VIETTEL_PLUS_APP_ID = os.environ.get("VIETTEL_PLUS_APP_ID") or None

    SYSTEM_LOG_SERVICE_CONFIG = ConfigSystemLogService()

    REDIS = ConfigRedis()
    URBOX_SECRET = os.environ.get("URBOX_SECRET") or ""


class TestingConfig(Config):
    TESTING = True


class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = False


class ProductionConfig(Config):
    DEBUG = True
    TESTING = False
    SQLALCHEMY_ECHO = False


configs = {
    "develop": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
    "default": DevelopmentConfig,
    "staging": ProductionConfig,
}

config_name = os.environ.get("APP_CONFIG") or "default"
config_app = configs[config_name]
