#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
import threading

from app.extensions import  queue_a12
from app.services.telegram_service import add_message_to_queue
from loguru import logger


class QueueService:
    def __init__(self):
        # Recover any pending messages from previous runs
        self.start_workers()

    def start_workers(self):
        logger.info("Starting workers...")
        workers = {
            "telegram": add_message_to_queue,
        }
        for task_type, worker_func in workers.items():
            t = threading.Thread(target=self.worker, args=(task_type, worker_func), daemon=True)
            t.start()

    def add_task(self, task_type, data):
        if task_type in queue_a12:
            queue_a12[task_type].put(data)
        else:
            print(f"Queue {task_type} không tồn tại!")

    def worker(self, task_type, worker_func):
        while True:
            logger.info(f'[QUEUE] {task_type} is running')
            data = queue_a12[task_type].get()
            worker_func(data)
            queue_a12[task_type].task_done()



queue_service = QueueService()
