#!/usr/bin/env python
# -*- coding: utf-8 -*-
import queue

from orm_alchemy import ActiveAlchemy
from app.config import config_app

from kafka_app import KafkaType, KafkaFactory
import redis
import mongoengine as mongoengine

from elasticapm.contrib.flask import ElasticAPM

db = ActiveAlchemy(config_app.DATABASE_URL, echo=config_app.SQLALCHEMY_ECHO)

# Set up a Kafka producer.
kafka_producer = KafkaFactory.create(
    KafkaType.PRODUCER, config_app.KAFKA_URL, config_app
)

redis_client = redis.Redis(
    host=config_app.REDIS.REDIS_HOST,
    port=int(config_app.REDIS.REDIS_PORT),
    db=int(config_app.REDIS.REDIS_DB),
)


# Set up a mongodb.
db_mongo = mongoengine.connect(host=config_app.MONGODB_URL)

# set up apm
apm = ElasticAPM()

queue_a12 = {
    "telegram": queue.Queue(),
}
