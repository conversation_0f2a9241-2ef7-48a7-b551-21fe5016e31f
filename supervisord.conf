[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/var/run/supervisord.pid


[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface


[unix_http_server]
file=/var/run/supervisor.sock


[supervisorctl]
serverurl=unix:///var/run/supervisor.sock


[program:flask]
command=gunicorn -w 2 -b 0.0.0.0:8000 "flask_manage:app" --timeout 120
directory=/home/<USER>/code
numprocs=1
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=false
redirect_stderr=true
redirect_stdout=true
stdout_logfile=/proc/1/fd/1
stderr_logfile=/proc/1/fd/2
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0

[program:kafka-worker]
command=bash -c "python kafka_manage.py"
directory=/home/<USER>/code
numprocs=1
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=false
stdout_logfile=/proc/1/fd/1
stderr_logfile=/proc/1/fd/2
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0


[program:flask_celery_worker]
command=celery -A celery_manage.app worker -l info --autoscale 2,1 --concurrency=1 -Ofair
directory=/home/<USER>/code
numproces=1
process_name=%(program_name)s_%(process_num)s
autostart=true
autorestart=true
stdout_logfile=/proc/1/fd/1
stderr_logfile=/proc/1/fd/2
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0


[program:flask_celery_beat]
command=celery -A celery_manage.app beat -l info
directory=/home/<USER>/code
numproces=1
process_name=$(program_name)s_%(process_num)s
autostart=true
autorestart=true
stdout_logfile=/proc/1/fd/1
stderr_logfile=/proc/1/fd/2
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0
