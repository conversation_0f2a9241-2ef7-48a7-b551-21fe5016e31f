#!/usr/bin/python
# -*- coding: utf-8 -*-
import datetime
import time

from app.const import FLASK_CELERY_TASK_SLEEP
from app.libs.base.utility import Utility


class TestUtility:
    def test_add_months(self):
        now = datetime.date(2020, 12, 22)
        actual = Utility.add_months(now, 1)
        expected = datetime.date(2021, 1, 22)
        assert expected == actual

    def test_sub_months(self):
        now = datetime.date(2020, 12, 22)
        actual = Utility.sub_months(now, 1)
        expected = datetime.date(2020, 11, 22)
        assert expected == actual

        now = datetime.date(2020, 1, 22)
        actual = Utility.sub_months(now, 1)
        expected = datetime.date(2019, 12, 22)
        assert expected == actual

    def test_diff_month(self):
        start_time = datetime.datetime.strptime(
            "2020-10-20 08:05:00", "%Y-%m-%d %H:%M:%S"
        )
        end_time = datetime.datetime.strptime(
            "2020-12-22 08:05:00", "%Y-%m-%d %H:%M:%S"
        )
        actual = Utility.diff_month(start_time, end_time)
        expected = 2
        assert actual == expected

    def test_diff_days(self):
        start_time = datetime.datetime.strptime(
            "2020-10-22 08:05:00", "%Y-%m-%d %H:%M:%S"
        )
        end_time = datetime.datetime.strptime(
            "2020-12-22 08:05:00", "%Y-%m-%d %H:%M:%S"
        )
        actual = Utility.diff_days(start_time, end_time)
        expected = 61
        assert actual == expected

    def test_sleep(self):
        start_time = time.time()
        Utility.sleep()
        end_time = time.time()
        actual = float("{:.2f}".format(end_time - start_time))
        expected = FLASK_CELERY_TASK_SLEEP
        assert actual == expected

    def test_convert_datetime_to_unixtimestamp(self):
        actual = Utility.convert_datetime_to_unixtimestamp(
            "2020-08-30T23:59:59.000Z", "%Y-%m-%dT%H:%M:%S.000Z"
        )
        expected = 1598806799
        assert expected == actual
