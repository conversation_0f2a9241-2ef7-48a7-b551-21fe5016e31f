#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest

from app.security import UrboxTripleDes

encode_data_test = [
    ("UB835286276", "fpeG4YfdeWs8oZQj2Eqn_w"),
    ("UB306550128", "4-MWSC8gyWBbZ2RsTepgKg"),
    ("DB333900321", "MuxOkHG056nANFFepVStTQ"),
    ("PY333901287", "muQGSZL_RFqJDInFBoofag"),
    ("PY333901248", "muQGSZL_RFrlYy2L-COc2w"),
    ("PY333901333", "muQGSZL_RFq58sN0m4ssHw"),
    ("DB333900317", "MuxOkHG056nB7LtuE5AIZg"),
    ("PY333901244", "muQGSZL_RFr5MsvXSU1iNw"),
    ("UB482835855", "DrWSWVsNcPbBHlCqnMda3Q"),
    ("UB374911468", "PSbdl-ZrTKX1R-ehJZP-xA"),
    ("PY333901286", "muQGSZL_RFpcu9RkZhjEVg"),
    ("DB333900323", "MuxOkHG056m3jmv8f-ecQQ"),
    ("PY333901241", "muQGSZL_RFrMxP48ODNtgQ"),
    ("PY333901297", "muQGSZL_RFqew2Yb1IciUw"),
    ("DB333900322", "MuxOkHG056kzspI5F3Wd3g"),
    ("DB333900319", "MuxOkHG056naPVFYeojo3A"),
    ("PY333901247", "muQGSZL_RFo8VgxzVsrixA"),
    ("PY333901307", "muQGSZL_RFq-hJGxJasP3w"),
    ("UB446175516", "5RujHbedO-ekIR9ZajFURQ"),
    ("PY333901288", "muQGSZL_RFoHTHAkrNtJDQ"),
    ("DB333900313", "MuxOkHG056noSNFs_U_hMw"),
    ("UB437353627", "UGCp17P53eBT-XuXMcmqPw"),
    ("UB909049159", "zpahodU_0QtbQIu5uBA-gA"),
    ("UB100872159", "dt93YNfClLHB1xsI7he9ZA"),
    ("UB303118805", "6BAtkXzh9_ita6cm9iC6Uw"),
    ("UB133203396", "bAZSxtZMvNfj8pHQO_ph5Q"),
    ("UB540421859", "P4wT__E6-6XsaiJqDEQRGA"),
    ("UB104499150", "Fvm7rwZsClqQrexmaT9OAg"),
    ("UB761765068", "kSFWF7KsMivJBtoqJzWpkg"),
    ("UB957327431", "hWTjDdx8VNNd0GT2kHX4rA"),
    ("UB783979352", "6fUTc7onOVjI6sM0Fag7Fg"),
    ("UB149623600", "j6_xfwde9i7gcKkzhMDcvg"),
]


class TestUrboxTripDes:
    @pytest.mark.parametrize("text, expected", encode_data_test)
    def test_encode(self, text, expected):
        txt_ecode = UrboxTripleDes.encode(text)
        assert txt_ecode == expected

    @pytest.mark.parametrize("expected, text", encode_data_test)
    def test_decode(self, expected, text):
        txt_decode = UrboxTripleDes.decode(text)
        assert txt_decode == expected
