#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest
import logging

from app.libs.apis.third_party_api.behavior_vmg_api import BehaviorVmgApi

logger = logging.getLogger(__name__)


class TestBehaviorVmgApi:
    def test_get_code_exception(self):
        product_code = "TEST_GET_CODE"

        with pytest.raises(Exception):
            vmg_api = BehaviorVmgApi()
            assert vmg_api.get_code(product_code, 0)
            assert vmg_api.get_code(product_code, 1001)
