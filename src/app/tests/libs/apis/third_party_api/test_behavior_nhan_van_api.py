#!/usr/bin/python
# -*- coding: utf-8 -*-
from libs.apis.third_party_api.behavior_nhan_van_api import BehaviorNhanVanApi
from libs.apis.third_party_api.context_code_api import ContextCodeApi

import pytest
import logging


logger = logging.getLogger(__name__)


class TestNhanVan:
    def test_get_param(self):
        nhanvanApi = BehaviorNhanVanApi()
        context = ContextCodeApi(nhanvanApi)
        list_codes = context.get_code(1, 1)
        assert 1 == 1
