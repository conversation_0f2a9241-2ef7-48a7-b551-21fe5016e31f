#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest

from app.libs.apis.third_party_api.behavior_the_coffeehouse_api import (
    BehaviorTheCoffeeHouseApi,
)
from app.security import UrboxTripleDes


class TestTheCoffeeHouseApi:
    def test_get_headers(self):
        the_coffeehouse_api = BehaviorTheCoffeeHouseApi()
        actual = the_coffeehouse_api.get_headers()
        expected = {
            "Content-type": "application/json",
            "Authorization": the_coffeehouse_api.author_code,
        }
        assert expected == actual

    def test_get_body_path_get_code(self):
        product_code = "ABC"
        the_coffeehouse_api = BehaviorTheCoffeeHouseApi()
        actual = the_coffeehouse_api.get_body_path_get_code(product_code)
        expected = dict(
            product_code=product_code,
            customer_name="",
            customer_phone="",
            secret_key=the_coffeehouse_api.secret_code,
        )
        assert expected == actual

    def test_get_code_exception(self):
        product_code = "TEST_GET_CODE"

        with pytest.raises(Exception):
            the_coffeehouse_api = BehaviorTheCoffeeHouseApi()
            assert the_coffeehouse_api.get_code(product_code, 0)
            assert the_coffeehouse_api.get_code(product_code, 1001)

    def test_get_code(self, mocker):
        product_code = "ABC"
        quantity = 1

        def requests_post(url, json, headers):
            class MockResponse:
                def __init__(self, json_data, status_code):
                    self.json_data = json_data
                    self.status_code = status_code

                def json(self):
                    return self.json_data

            return MockResponse(
                dict(
                    barcode="TQC000000000",
                    expire_date="2020-08-30T23:59:59.000Z",
                ),
                200,
            )

        mocker.patch(
            "libs.apis.third_party_api.behavior_the_coffeehouse_api.requests.post",
            requests_post,
        )
        the_coffeehouse_api = BehaviorTheCoffeeHouseApi()
        actual = the_coffeehouse_api.get_code(product_code, quantity)
        expected = [
            dict(
                serial=None,
                pin=None,
                codex=UrboxTripleDes.encode("TQC000000000"),
                expired=1598806799,
            )
        ]
        assert expected == actual
