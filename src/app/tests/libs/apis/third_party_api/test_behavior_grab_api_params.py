#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest
import logging
import time
import json
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from app.libs.apis.third_party_api.behavior_grab_api import BehaviorGrabApi
from app.libs.base.utility import Utility

logger = logging.getLogger(__name__)


class TestBehaviorGrabApiParams:
    """
    Test cases for BehaviorGrabApi.get_code function focusing on parameter variations:
    - product_code
    - quantity
    - price
    - effective_date
    - expired
    - po_request_id
    """

    @pytest.fixture
    def grab_api(self):
        """Fixture to create a BehaviorGrabApi instance"""
        return BehaviorGrabApi()

    @pytest.fixture
    def base_product_info(self):
        """Fixture for base product info from payload-grab-test.json"""
        # Use current time + 1 day for effective_date and current time + 30 days for expired
        tomorrow = int(time.time()) + 86400
        next_month = int(time.time()) + 86400 * 30

        return {
            "product_id": 10328,
            "supplier_id": 191,
            "product_supplier_id": 2383,
            "code_prefix": "UB",
            "code_length": 9,
            "product_code": "grab_food",
            "scheme_code": "",
            "po_code": "MPO5594224",
            "quantity": 1,
            "price": 10000,
            "product_parent_id": 35,
            "order_id": 2502,
            "effective_date": tomorrow,
            "expired": next_month,
            "max_codes_per_call": 1000,
            "retry_transaction_id": "",
            "meta_data": None,
            "po_request_id": "MPO1744945519",
            "supplier_order_id": 0,
            "request_round": "product-MPO5594224-10328-round-1"
        }

    # Helper method to setup common mocks
    def setup_mocks(self, mock_post):
        """Setup common mocks for successful API response"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "orderID": "order_123",
            "reference": "ref_123",
            "gift": {
                "codes": ["GRAB123456"]
            }
        }
        mock_response.text = json.dumps({
            "orderID": "order_123",
            "reference": "ref_123",
            "gift": {
                "codes": ["GRAB123456"]
            }
        })
        mock_response.headers = {}
        mock_post.return_value = mock_response

    # Test 1: Test with different product_code values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_product_codes(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                    mock_update_logs, mock_create_logs, mock_create_order_log,
                                    mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different product_code values.

        This test verifies that the API can handle different Grab product codes
        such as grab_food, grab_express, grab_mart, etc.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Test with grab_food (default)
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with grab_express
        product_info = base_product_info.copy()
        product_info["product_code"] = "grab_express"
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with grab_mart
        product_info = base_product_info.copy()
        product_info["product_code"] = "grab_mart"
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with multiple product codes (comma-separated)
        product_info = base_product_info.copy()
        product_info["product_code"] = "grab_food,grab_express"
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

    # Test 2: Test with different quantity values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_quantities(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                 mock_update_logs, mock_create_logs, mock_create_order_log,
                                 mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different quantity values.

        This test verifies that the API can handle different quantities of codes
        to be purchased, including the maximum allowed per call.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Test with quantity = 1 (default)
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with quantity = 10
        product_info = base_product_info.copy()
        product_info["quantity"] = 10
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with quantity = 100
        product_info = base_product_info.copy()
        product_info["quantity"] = 100
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with quantity > max_codes_per_call (should be limited to max_codes_per_call)
        product_info = base_product_info.copy()
        product_info["quantity"] = 2000  # Greater than max_codes_per_call (1000)
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Verify that quantity was limited to max_codes_per_call
        # This is checking the body parameter in the request.post call
        call_args = mock_post.call_args[1]
        request_body = call_args['json']
        assert request_body['order']['gift']['quantity'] == 1000

    # Test 3: Test with different price values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_prices(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                             mock_update_logs, mock_create_logs, mock_create_order_log,
                             mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different price values.

        This test verifies that the API can handle different price values for the Grab codes,
        from small amounts to large amounts.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Test with price = 10000 (default)
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with price = 20000
        product_info = base_product_info.copy()
        product_info["price"] = 20000
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with price = 50000
        product_info = base_product_info.copy()
        product_info["price"] = 50000
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with price = 100000
        product_info = base_product_info.copy()
        product_info["price"] = 100000
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

    # Test 4: Test with different effective_date values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_effective_dates(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                      mock_update_logs, mock_create_logs, mock_create_order_log,
                                      mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different effective_date values.

        This test verifies that the API can handle different effective dates for the Grab codes,
        including dates in the near future and far future.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Current time plus 1 day
        tomorrow = int(time.time()) + 86400
        # Current time plus 7 days
        next_week = int(time.time()) + 86400 * 7
        # Current time plus 30 days
        next_month = int(time.time()) + 86400 * 30

        # Test with default effective_date
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with tomorrow as effective_date
        product_info = base_product_info.copy()
        product_info["effective_date"] = tomorrow
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with next week as effective_date
        product_info = base_product_info.copy()
        product_info["effective_date"] = next_week
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with next month as effective_date
        product_info = base_product_info.copy()
        product_info["effective_date"] = next_month
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

    # Test 5: Test with different expired values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_expired_dates(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                    mock_update_logs, mock_create_logs, mock_create_order_log,
                                    mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different expired values.

        This test verifies that the API can handle different expiration dates for the Grab codes,
        including dates in the near future and far future.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Current time plus 30 days
        next_month = int(time.time()) + 86400 * 30
        # Current time plus 90 days
        three_months = int(time.time()) + 86400 * 90
        # Current time plus 365 days
        one_year = int(time.time()) + 86400 * 365

        # Test with default expired date
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with next month as expired date
        product_info = base_product_info.copy()
        product_info["expired"] = next_month
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with three months as expired date
        product_info = base_product_info.copy()
        product_info["expired"] = three_months
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with one year as expired date
        product_info = base_product_info.copy()
        product_info["expired"] = one_year
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

    # Test 6: Test with different po_request_id values
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_different_po_request_ids(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                     mock_update_logs, mock_create_logs, mock_create_order_log,
                                     mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with different po_request_id values.

        This test verifies that the API can handle different purchase order request IDs,
        which are used for tracking and reference purposes.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Test with default po_request_id
        product_info = base_product_info.copy()
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with different po_request_id
        product_info = base_product_info.copy()
        product_info["po_request_id"] = "MPO9876543"
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Test with another po_request_id
        product_info = base_product_info.copy()
        product_info["po_request_id"] = "MPO1234567"
        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

    # Test 7: Test with invalid parameter combinations
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('requests.post')
    def test_invalid_parameter_combinations(self, mock_post, mock_create_order_log, mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with invalid parameter combinations.

        This test verifies that the API properly validates parameter combinations
        and returns appropriate error messages.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Setup mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "orderID": "order_123",
            "reference": "ref_123",
            "gift": {
                "codes": ["GRAB123456"]
            }
        }
        mock_response.text = json.dumps({
            "orderID": "order_123",
            "reference": "ref_123",
            "gift": {
                "codes": ["GRAB123456"]
            }
        })
        mock_response.headers = {}
        mock_post.return_value = mock_response

        # Test with effective_date after expired date
        product_info = base_product_info.copy()
        product_info["effective_date"] = int(time.time()) + 86400 * 30  # 30 days from now
        product_info["expired"] = int(time.time()) + 86400 * 15  # 15 days from now

        codes, message = grab_api.get_code(product_info)
        assert len(codes) == 0
        assert "effective_date phải nhỏ hơn expired" in message

        # Test with expired date in the past
        product_info = base_product_info.copy()
        product_info["expired"] = int(time.time()) - 86400  # Yesterday

        codes, message = grab_api.get_code(product_info)
        assert len(codes) == 0
        assert "expired phải lớn hơn ngày hiện tại" in message

        # Test with effective_date in the past
        product_info = base_product_info.copy()
        product_info["effective_date"] = int(time.time()) - 86400  # Yesterday

        # For this test, we need to patch the BehaviorGrabApi.__get_headers method to prevent it from proceeding
        # with the API call when validation fails
        with patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi._BehaviorGrabApi__get_headers') as mock_get_headers:
            # Make the __get_headers method raise an exception to simulate validation failure
            mock_get_headers.side_effect = Exception("effective_date phải lớn hơn ngày hiện tại")

            codes, message = grab_api.get_code(product_info)
            assert len(codes) == 0
            assert "effective_date phải lớn hơn ngày hiện tại" in message

    # Test 8: Test with multiple parameter changes at once
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.get_token')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_multiple_parameter_changes(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                       mock_update_logs, mock_create_logs, mock_create_order_log,
                                       mock_before_get_code, mock_get_token, grab_api, base_product_info):
        """
        Test get_code with multiple parameter changes at once.

        This test verifies that the API can handle multiple parameter changes simultaneously,
        which is a common real-world scenario.
        """
        # Mock token retrieval
        mock_get_token.return_value = "mocked_token"

        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        self.setup_mocks(mock_post)

        # Test with multiple parameter changes
        product_info = base_product_info.copy()
        product_info["product_code"] = "grab_mart"
        product_info["quantity"] = 50
        product_info["price"] = 25000
        product_info["effective_date"] = int(time.time()) + 86400 * 2  # 2 days from now
        product_info["expired"] = int(time.time()) + 86400 * 60  # 60 days from now
        product_info["po_request_id"] = "MPO8765432"

        codes, message = grab_api.get_code(product_info)
        assert message == "Success"

        # Verify the request was made with the correct parameters
        call_args = mock_post.call_args[1]
        request_body = call_args['json']
        assert request_body['order']['gift']['inventories'] == ["grab_mart"]
        assert request_body['order']['gift']['quantity'] == 50
        assert request_body['order']['gift']['value'] == 25000
