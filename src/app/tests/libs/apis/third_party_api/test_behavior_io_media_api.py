#!/usr/bin/python
# -*- coding: utf-8 -*-
import unittest

from app.libs.apis.third_party_api.behavior_io_media_api import BehaviorIoMediaApi
from app.security import UrboxTripleDes


class TestIoMediaApi(unittest.TestCase):
    def test_get_headers(self):
        io_media = BehaviorIoMediaApi()
        actual_header = io_media.get_headers()
        expected = {"Content-type": "application/json"}
        assert expected == actual_header

    def test_get_code_exception_quantity(self):
        product_code = "TEST_GET_CODE"
        actual_message_when_qty_is_0 = "Số lượng tối thiểu phải là 1."
        actual_message_when_qty_is_1001 = "Số lượng không được vượt quá 1000."
        io_media = BehaviorIoMediaApi()

        with self.assertRaises(Exception) as cm:
            io_media.get_code(product_code, 0)
        self.assertEqual(actual_message_when_qty_is_0, str(cm.exception))
        with self.assertRaises(Exception) as cm:
            io_media.get_code(product_code, 1001)
        self.assertEqual(actual_message_when_qty_is_1001, str(cm.exception))

    def test_get_code(self, mocker):
        product_code = "ABC"
        quantity = 1

        def requests_post(url, json, headers):
            class MockResponse:
                def __init__(self, json_data, status_code):
                    self.json_data = json_data
                    self.status_code = status_code

                def json(self):
                    return self.json_data

            return MockResponse(
                dict(
                    barcode="TQC000000000",
                    expire_date="2020-08-30T23:59:59.000Z",
                ),
                200,
            )

        mocker.patch("libs.apis.third_party_api.io_media.requests.post", requests_post)
        io_media = BehaviorIoMediaApi()
        actual = io_media.get_code(product_code, quantity)
        expected = [
            dict(
                serial=None,
                pin=None,
                codex=UrboxTripleDes.encode("TQC000000000"),
                expired=1598806799,
            )
        ]
        assert expected == actual
