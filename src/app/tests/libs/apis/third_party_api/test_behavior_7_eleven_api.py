#!/usr/bin/python
# -*- coding: utf-8 -*-
from app.libs.apis.third_party_api.behavior_7_eleven_api import Behavior7<PERSON>levenApi
from app.security import UrboxTripleDes


class TestIoMediaApi:
    def test_get_headers(self):
        io_media = Behavior7ElevenApi()
        actual = io_media.get_headers()
        expected = {
            "Content-type": "application/json",
            "Authorization": "8c6ce46fda72bb95de191a72b7abbb68f1d2607",
        }
        assert expected == actual

    def test_get_code_exception(self):
        product_code = "UB210150"
        eleven = Behavior7ElevenApi()
        eleven.get_code(product_code, 0)
        assert eleven.get_code(product_code, 0) == []
        assert eleven.get_code(product_code, 1001) == []

    def test_get_code(self, mocker):
        product_code = "UB210150"
        quantity = 1

        def requests_post(url, json, headers):
            class MockResponse:
                def __init__(self, json_data, status_code):
                    self.json_data = json_data
                    self.status_code = status_code

                def json(self):
                    return self.json_data

            return MockResponse(
                dict(
                    voucher_code="TQC000000000",
                    qr_text="2020-08-30T23:59:59.000Z",
                    quantity=1,
                    expired_at="2020-08-30T23:59:59.000Z",
                ),
                200,
            )

        mocker.patch(
            "libs.apis.third_party_api.behavior_7_eleven_api.requests.post",
            requests_post,
        )
        io_media = Behavior7ElevenApi()
        actual = io_media.get_code(product_code, quantity)
        expected = [
            dict(
                serial=None,
                pin=None,
                codex=UrboxTripleDes.encode("TQC000000000"),
                expired=1598806799,
            )
        ]
        assert expected == actual
