#!/usr/bin/python
# -*- coding: utf-8 -*-
from requests.auth import HTTPBasicAuth
import pytest

from app.libs.apis.third_party_api.behavior_jollibee_api import Behavior<PERSON>ollibeeApi
from app.security import UrboxTripleDes


class TestBehaviorJollibeeApi:
    def test_get_headers(self):
        jollibee_api = BehaviorJollibeeApi()
        actual = jollibee_api.get_headers()
        expected = {
            "Content-type": "application/json",
            "X-ApiKey": jollibee_api.api_key,
        }
        assert expected == actual

    # def test_get_body_path_get_code(self, mocker):
    #     product_code = 'TEST_BODY'
    #     quantity     = 1
    #     now_unixtimestamps = 1610508532 # 2021-01-13 10:28:52
    #     mocker.patch.object(libs.apis.third_party_api.behavior_jollibee_api.DatetimeDao, attribute='get_now_unixtimestamp', return_value=now_unixtimestamps)
    #     jollibee_api = BehaviorJollibeeApi()
    #     actual = jollibee_api.get_body_path_get_code(product_code, quantity)
    #     expected = dict(
    #         isMode   = 1,
    #         Quantity = quantity,
    #         Amount   = product_code,
    #         EffectiveFrom = "{year}{month:02d}{day:02d}".format(year=2021, month=1, day=13),
    #         EffectiveTo   = "{year}{month:02d}{day:02d}".format(year=2021, month=5, day=30)
    #     )
    #     assert expected == actual

    def test_get_authors(self):
        jollibee_api = BehaviorJollibeeApi()
        actual = jollibee_api.get_authors()
        expected = HTTPBasicAuth(jollibee_api.author_code, jollibee_api.secret_code)
        assert expected == actual

    def test_body_path_remove_code(self):
        code = "TEST_CODE"
        jollibee_api = BehaviorJollibeeApi()
        actual = jollibee_api.get_body_path_remove_code(code)
        expected = dict(VoucherCode=code)
        assert expected == actual

    def test_get_code_exception(self):
        product_code = "TEST_GET_CODE"

        with pytest.raises(Exception):
            jollibee_api = BehaviorJollibeeApi()
            assert jollibee_api.get_code(product_code, 0)
            assert jollibee_api.get_code(product_code, 1001)

    def test_get_code_success(self, mocker):
        product_code = "TEST_GET_CODE"
        quantity = 2

        def request_dao(url, *args, **kwargs):
            class MockResponse:
                def __init__(self, json_data, status_code):
                    self.json_data = json_data
                    self.status_code = status_code

                def json(self):
                    return self.json_data

            return MockResponse(
                [
                    dict(
                        number="UB100K328B2EE",
                        amount=product_code,
                        createdate="2020-08-21T00:00:00",
                        startdate="2020-08-21T00:00:00",
                        enddate="2020-12-29T23:59:00",
                        useddate="0001-01-01T00:00:00",
                        status=0,
                        vouchertype=None,
                    ),
                    dict(
                        number="UB100K328B2EE",
                        amount=product_code,
                        createdate="2020-08-21T00:00:00",
                        startdate="2020-08-21T00:00:00",
                        enddate="2020-12-29T23:59:00",
                        useddate="0001-01-01T00:00:00",
                        status=0,
                        vouchertype=None,
                    ),
                ],
                200,
            )

        mocker.patch(
            "libs.apis.third_party_api.behavior_jollibee_api.requests.get", request_dao
        )

        jollibee_api = BehaviorJollibeeApi()
        actual = jollibee_api.get_code(product_code, quantity)
        expected = [
            dict(
                serial=None,
                pin=None,
                codex=UrboxTripleDes.encode("UB100K328B2EE"),
                expired=1609261140,
            ),
            dict(
                serial=None,
                pin=None,
                codex=UrboxTripleDes.encode("UB100K328B2EE"),
                expired=1609261140,
            ),
        ]
        assert expected == actual

    def test_remove_code(self, mocker):
        codes = ["UB100K328B2EE", "UB100K328B2EE"]

        def request_dao(url, *args, **kwargs):
            class MockResponse:
                def __init__(self, json_data, status_code):
                    self.json_data = json_data
                    self.status_code = status_code

                def json(self):
                    return self.json_data

            return MockResponse([], 200)

        mocker.patch(
            "libs.apis.third_party_api.behavior_jollibee_api.requests.get", request_dao
        )

        jollibee_api = BehaviorJollibeeApi()
        actual = jollibee_api.remove_code(codes)
        expected = codes
        assert expected == actual
