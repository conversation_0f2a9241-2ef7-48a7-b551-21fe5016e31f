#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest
import logging
import time
import json
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from app.libs.apis.third_party_api.behavior_grab_api import BehaviorGrabApi
from app.libs.base.utility import Utility

logger = logging.getLogger(__name__)


class TestBehaviorGrabApi:

    @pytest.fixture
    def grab_api(self):
        """Fixture to create a BehaviorGrabApi instance"""
        return BehaviorGrabApi()

    @pytest.fixture
    def valid_product_info(self):
        """Fixture for valid product info"""
        # Current time plus 1 day for effective_date
        effective_date = int(time.time()) + 86400
        # Current time plus 30 days for expired
        expired = int(time.time()) + 86400 * 30

        return {
            "product_id": 10328,
            "supplier_id": 191,
            "product_supplier_id": 2383,
            "code_prefix": "UB",
            "code_length": 9,
            "product_code": "grab_food",
            "scheme_code": "",
            "po_code": "MPO5594224",
            "quantity": 1,
            "price": 10000,
            "product_parent_id": 35,
            "order_id": 2502,
            "effective_date": effective_date,
            "expired": expired,
            "max_codes_per_call": 1000,
            "retry_transaction_id": "",
            "meta_data": None,
            "po_request_id": "MPO1744945519",
            "supplier_order_id": 0,
            "request_round": "product-MPO5594224-10328-round-1"
        }

    # Test 1: Test initialization of BehaviorGrabApi
    def test_init(self, grab_api):
        """Test that BehaviorGrabApi initializes correctly with all required attributes"""
        assert grab_api.title == "GrabApi"
        assert hasattr(grab_api, 'client_id')
        assert hasattr(grab_api, 'client_secret')
        assert hasattr(grab_api, 'grand_type')
        assert hasattr(grab_api, 'scope')
        assert hasattr(grab_api, 'host')
        assert hasattr(grab_api, 'path_get_code')
        assert hasattr(grab_api, 'path_authen')
        assert hasattr(grab_api, 'path_gift_links')

    # Test 2: Test get_code with invalid product_info (missing required fields)
    def test_get_code_missing_fields(self, grab_api):
        """Test get_code with missing required fields in product_info"""
        with pytest.raises(Exception):
            # Missing required fields
            product_info = {}
            grab_api.get_code(product_info)

    # Test 3: Test get_code with invalid product_code
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_invalid_product_code(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with invalid product_code (empty)"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set empty product_code
        product_info = valid_product_info.copy()
        product_info["product_code"] = ""

        with pytest.raises(Exception):
            grab_api.get_code(product_info)

    # Test 4: Test get_code with zero quantity
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_zero_quantity(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with quantity = 0"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set quantity to 0
        product_info = valid_product_info.copy()
        product_info["quantity"] = 0

        with pytest.raises(Exception):
            grab_api.get_code(product_info)

    # Test 5: Test get_code with zero price
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_zero_price(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with price = 0"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set price to 0
        product_info = valid_product_info.copy()
        product_info["price"] = 0

        with pytest.raises(Exception):
            grab_api.get_code(product_info)

    # Test 6: Test get_code with effective_date in the past
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_past_effective_date(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with effective_date in the past"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set effective_date to a past date
        product_info = valid_product_info.copy()
        product_info["effective_date"] = int(time.time()) - 86400  # Yesterday

        # This should return a warning but not fail
        codes, message = grab_api.get_code(product_info)
        assert len(codes) == 0
        assert "effective_date phải lớn hơn ngày hiện tại" in message

    # Test 7: Test get_code with expired date in the past
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_past_expired_date(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with expired date in the past"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set expired to a past date
        product_info = valid_product_info.copy()
        product_info["expired"] = int(time.time()) - 86400  # Yesterday

        codes, message = grab_api.get_code(product_info)
        assert len(codes) == 0
        assert "expired phải lớn hơn ngày hiện tại" in message

    # Test 8: Test get_code with effective_date after expired date
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_effective_after_expired(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with effective_date after expired date"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set effective_date after expired
        product_info = valid_product_info.copy()
        product_info["effective_date"] = int(time.time()) + 86400 * 30  # 30 days from now
        product_info["expired"] = int(time.time()) + 86400 * 15  # 15 days from now

        codes, message = grab_api.get_code(product_info)
        assert len(codes) == 0
        assert "effective_date phải nhỏ hơn expired" in message

    # Test 9: Test get_code with empty po_request_id
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    def test_get_code_empty_po_request_id(self, mock_create_order_log, mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with empty po_request_id"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Set empty po_request_id
        product_info = valid_product_info.copy()
        product_info["po_request_id"] = ""

        # This should not cause an error as po_request_id is not validated in get_code
        # but might be used in other parts of the system
        grab_api.get_code(product_info)

    # Test 10: Test get_code with successful API response
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_order_log')
    @patch('requests.post')
    def test_get_code_successful_response(self, mock_post, mock_update_order_log, mock_save_order_detail_log,
                                        mock_update_logs, mock_create_logs, mock_create_order_log,
                                        mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with successful API response"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "orderID": "order_123",
            "reference": "ref_123"
        }
        mock_response.text = json.dumps({
            "orderID": "order_123",
            "reference": "ref_123"
        })
        mock_response.headers = {}
        mock_post.return_value = mock_response

        codes, message = grab_api.get_code(valid_product_info)
        assert message == "Success"
        assert len(codes) == 0  # No codes are returned directly, they are fetched separately

        # Verify all the mocked methods were called
        mock_before_get_code.assert_called_once()
        mock_create_order_log.assert_called_once()
        mock_create_logs.assert_called_once()
        mock_update_logs.assert_called_once()
        mock_save_order_detail_log.assert_called_once()
        mock_update_order_log.assert_called_once()
        mock_post.assert_called_once()

    # Test 11: Test get_code with failed API response
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.update_logs')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.save_order_detail_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.Utility.send_telegram_message_to_group')
    @patch('requests.post')
    def test_get_code_failed_response(self, mock_post, mock_send_telegram, mock_save_order_detail_log,
                                    mock_update_logs, mock_create_logs, mock_create_order_log,
                                    mock_before_get_code, grab_api, valid_product_info):
        """Test get_code with failed API response"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345
        mock_create_logs.return_value = {"log_id": 1}

        # Mock failed response
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "message": "Invalid request"
        }
        mock_response.text = json.dumps({
            "message": "Invalid request"
        })
        mock_response.headers = {}
        mock_post.return_value = mock_response

        codes, message = grab_api.get_code(valid_product_info)
        assert "Xảy ra lỗi khi lấy code Grab" in message
        assert len(codes) == 0

        # Verify all the mocked methods were called
        mock_before_get_code.assert_called_once()
        mock_create_order_log.assert_called_once()
        mock_create_logs.assert_called_once()
        mock_update_logs.assert_called_once()
        mock_save_order_detail_log.assert_called_once()
        mock_send_telegram.assert_called_once()
        mock_post.assert_called_once()

    # Test 12: Test get_code with exception during API call
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.before_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.create_order_log')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.BehaviorGrabApi.after_get_code')
    @patch('app.libs.apis.third_party_api.behavior_grab_api.Utility.send_telegram_message_to_group')
    @patch('requests.post')
    def test_get_code_exception_during_api_call(self, mock_post, mock_send_telegram, mock_after_get_code,
                                              mock_create_order_log, mock_before_get_code,
                                              grab_api, valid_product_info):
        """Test get_code with exception during API call"""
        mock_before_get_code.return_value = "transaction_id_123"
        mock_create_order_log.return_value = 12345

        # Mock exception during API call
        mock_post.side_effect = Exception("Connection error")

        codes, message = grab_api.get_code(valid_product_info)
        assert "Xảy ra lỗi khi lấy code Grab" in message
        assert "Connection error" in message
        assert len(codes) == 0

        # Verify all the mocked methods were called
        mock_before_get_code.assert_called_once()
        mock_create_order_log.assert_called_once()
        mock_after_get_code.assert_called_once()
        mock_send_telegram.assert_called_once()
        mock_post.assert_called_once()

    # Test 13: Test get_gift_links with successful response
    @patch('requests.get')
    @patch('app.repositories.log_request_api.log_request_api_repo.get_by_transaction')
    def test_get_gift_links_successful(self, mock_get_by_transaction, mock_get, grab_api):
        """Test get_gift_links with successful response"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "gifts": [
                {
                    "short_code": "ABC123",
                    "end_date": "2023-12-31T23:59:59Z",
                    "value": 10000
                },
                {
                    "short_code": "DEF456",
                    "end_date": "2023-12-31T23:59:59Z",
                    "value": 20000
                }
            ]
        }
        mock_response.text = json.dumps(mock_response.json.return_value)
        mock_get.return_value = mock_response

        # Mock log repository
        mock_log = MagicMock()
        mock_log.response_data = {"response_data": {}}
        mock_get_by_transaction.return_value = mock_log

        codes, message = grab_api.get_gift_links("order_123", "transaction_123")

        assert len(codes) == 2
        assert codes[0]["codex"] == "ABC123"
        assert codes[0]["value"] == 10000
        assert codes[1]["codex"] == "DEF456"
        assert codes[1]["value"] == 20000
        assert message == ""

        # Verify mocked methods were called
        mock_get.assert_called_once()
        mock_get_by_transaction.assert_called_once()

    # Test 14: Test get_gift_links with empty gifts
    @patch('requests.get')
    @patch('app.repositories.log_request_api.log_request_api_repo.get_by_transaction')
    def test_get_gift_links_empty_gifts(self, mock_get_by_transaction, mock_get, grab_api):
        """Test get_gift_links with empty gifts array"""
        # Mock response with empty gifts
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "gifts": [],
            "message": "No gifts found"
        }
        mock_response.text = json.dumps(mock_response.json.return_value)
        mock_get.return_value = mock_response

        # Mock log repository
        mock_log = MagicMock()
        mock_log.response_data = {"response_data": {}}
        mock_get_by_transaction.return_value = mock_log

        codes, message = grab_api.get_gift_links("order_123", "transaction_123")

        assert len(codes) == 0
        assert message == "No gifts found"

        # Verify mocked methods were called
        mock_get.assert_called_once()
        mock_get_by_transaction.assert_called_once()

    # Test 15: Test get_gift_links with failed response
    @patch('requests.get')
    @patch('app.repositories.log_request_api.log_request_api_repo.get_by_transaction')
    def test_get_gift_links_failed_response(self, mock_get_by_transaction, mock_get, grab_api):
        """Test get_gift_links with failed response"""
        # Mock failed response
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "message": "Invalid request"
        }
        mock_response.text = json.dumps(mock_response.json.return_value)
        mock_get.return_value = mock_response

        # Mock log repository
        mock_log = MagicMock()
        mock_log.response_data = {"response_data": {}}
        mock_get_by_transaction.return_value = mock_log

        codes, message = grab_api.get_gift_links("order_123", "transaction_123")

        assert len(codes) == 0
        assert message == "Không lấy được gift links"

        # Verify mocked methods were called
        mock_get.assert_called_once()
        mock_get_by_transaction.assert_called_once()

    # Test 16: Test get_gift_links with exception
    @patch('requests.get')
    def test_get_gift_links_exception(self, mock_get, grab_api):
        """Test get_gift_links with exception"""
        # Mock exception
        mock_get.side_effect = Exception("Connection error")

        codes, message = grab_api.get_gift_links("order_123", "transaction_123")

        assert len(codes) == 0
        assert message == "Connection error"

        # Verify mocked method was called
        mock_get.assert_called_once()

    # Test 17: Test get_token with successful response
    @patch('requests.post')
    def test_get_token_successful(self, mock_post, grab_api):
        """Test get_token with successful response"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_token_123",
            "expires_in": 3600
        }
        mock_post.return_value = mock_response

        # Initial token should be empty
        assert grab_api.token == ""

        # Call get_token
        grab_api.get_token()

        # Token should be updated
        assert grab_api.token == "test_token_123"
        assert grab_api.expire_token > int(time.time())

        # Verify mocked method was called
        mock_post.assert_called_once()

    # Test 18: Test get_token with failed response
    @patch('requests.post')
    def test_get_token_failed_response(self, mock_post, grab_api):
        """Test get_token with failed response"""
        # Mock failed response
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "error": "invalid_request"
        }
        mock_post.return_value = mock_response

        # Call get_token should raise exception
        with pytest.raises(Exception) as excinfo:
            grab_api.get_token()

        assert "Không lấy được token" in str(excinfo.value)

        # Verify mocked method was called
        mock_post.assert_called_once()

    # Test 19: Test get_token with exception
    @patch('requests.post')
    def test_get_token_exception(self, mock_post, grab_api):
        """Test get_token with exception"""
        # Mock exception
        mock_post.side_effect = Exception("Connection error")

        # Call get_token should raise exception
        with pytest.raises(Exception) as excinfo:
            grab_api.get_token()

        assert "Exception không lấy được token" in str(excinfo.value)

        # Verify mocked method was called
        mock_post.assert_called_once()

    # Test 20: Test get_balance method
    @patch('requests.get')
    def test_get_balance(self, mock_get, grab_api):
        """Test get_balance method"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "balance": 1000000
        }
        mock_get.return_value = mock_response

        # Call get_balance
        balance = grab_api.get_balance()

        assert balance == {"balance": 1000000}

        # Verify mocked method was called
        mock_get.assert_called_once()

    # Test 21: Test get_balance with exception
    @patch('requests.get')
    def test_get_balance_exception(self, mock_get, grab_api):
        """Test get_balance with exception"""
        # Mock exception
        mock_get.side_effect = Exception("Connection error")

        # Call get_balance should raise exception
        with pytest.raises(Exception) as excinfo:
            grab_api.get_balance()

        assert "Exception không lấy được balance" in str(excinfo.value)

        # Verify mocked method was called
        mock_get.assert_called_once()
