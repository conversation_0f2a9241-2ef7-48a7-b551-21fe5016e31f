#!/usr/bin/python
# -*- coding: utf-8 -*-
import time
import pytest

from app.libs.apis.third_party_api.behavior_urbox_api import BehaviorUrboxApi


class TestBehaviorUrboxApi:
    def test_get_code_exception(self):
        product_code = "TEST_GET_CODE"

        with pytest.raises(Exception):
            urbox_api = BehaviorUrboxApi()
            assert urbox_api.get_code(product_code, 0)
            assert urbox_api.get_code(product_code, 1001)

    def test_get_code_success(self):
        start = int(time.time())
        urbox_api = BehaviorUrboxApi()
        codes = urbox_api.get_code("ABC", 1000)
        assert len(codes) == 1000
