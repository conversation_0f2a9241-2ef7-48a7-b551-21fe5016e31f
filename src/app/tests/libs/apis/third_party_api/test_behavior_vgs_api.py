#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest
import logging

from app.libs.apis.third_party_api.behavior_vietguys_api import BehaviorVietGuysApi

logger = logging.getLogger(__name__)


class TestBehaviorVietGuysApi:
    def test_get_code_exception(self):

        with pytest.raises(Exception):
            vendor = BehaviorVietGuysApi()
            product_info = dict(
                quantity=1,
                product_id = 4538,
                price = 100000,
                product_code = 'vietel',
                product_telco = 'vietel',
                supplier_id=233,
                product_supplier_id=233,
            )
            assert vendor.get_code(product_info)
