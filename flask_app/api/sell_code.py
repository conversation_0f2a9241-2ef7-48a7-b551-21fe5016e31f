#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask_restful import Resource
from flask_restful.reqparse import Argument
from loguru import logger

from app.api import CodexService
from app.decorators import sqlalchemy_session, parse_params
from app.extensions import db
from app.utils import remove_none_in_dict


class FlaskSellCode(Resource):
    @parse_params(
        Argument(
            "transaction_id",
            location=["values", "json"],
            required=True,
            help="transaction_id",
            type=str,
            default=None,
        ),
        Argument(
            "products",
            location=["values", "json"],
            required=True,
            help="products",
            type=dict,
            default=None,
            action="append"
        )
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        try:
            logger.info("Request")
            logger.info(kwargs)
            codes = CodexService.sell(**remove_none_in_dict(kwargs))
            if codes is not False:
                return {"success": True, "code": 200, "data": codes}, 200
            else:
                return {
                    "success": False,
                    "code": 400,
                    "message": "Input error",
                    "error": {"errors": "<PERSON><PERSON> liệu truyền lên không đúng định dạng"},
                }, 400
        except Exception as e:
            logger.exception(e)
