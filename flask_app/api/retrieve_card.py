#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask_restful import Resource
from flask_restful.reqparse import Argument
from loguru import logger

from app.decorators import sqlalchemy_session, parse_params
from app.extensions import db
from app.libs.apis.third_party_api.behavior_io_media_api import BehaviorIoMediaApi


class FlaskRetrieveCard(Resource):
    @parse_params(
        Argument(
            "transaction_id",
            location=["values", "json"],
            required=True,
            help="transaction_id",
            type=str,
            default=None,
        ),
    )
    @sqlalchemy_session(db)
    def post(self, **kwargs):
        #return {"success": True, "code": 200, "data": None}, 200
        try:
            io_media = BehaviorIoMediaApi()
            codes = io_media.retrieve_card(kwargs.get("transaction_id"))

            if codes is not False:
                return {"success": True, "code": 200, "data": codes}, 200
            else:
                return {
                    "success": False,
                    "code": 400,
                    "message": "Input error",
                    "error": {"errors": "<PERSON><PERSON> liệu truyền lên không đúng định dạng"},
                }, 400
        except Exception as e:
            logger.exception(e)
