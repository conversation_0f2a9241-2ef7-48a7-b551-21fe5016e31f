#!/usr/bin/env python
# -*- coding: utf-8 -*-
from flask import Blueprint
from flask_restful import Api

from flask_app.api.get_code import <PERSON>laskGetCode, FlaskGetCodeByOrder, FlaskGetCodeWebhook, FlaskVietguysWebhook, \
    FlaskGrabWebhook, FlaskPOOrderCode, FlaskTriggerRetryGetCode, FlaskCheckBalance
from flask_app.api.ping import FlaskPing
from flask_app.api.retrieve_card import FlaskRetrieveCard
from flask_app.api.sell_code import FlaskSellCode
from flask_app.api.trigger_generate_code import FlaskTriggerGenerateCode

bp = Blueprint("api", __name__, url_prefix="/v1")

api = Api(bp)
api.add_resource(FlaskPing, "/ping")
api.add_resource(FlaskTriggerGenerateCode, "/trigger-generate-code")
api.add_resource(FlaskGetCode, "/get_code")
api.add_resource(FlaskRetrieveCard, "/retrieve-card")
api.add_resource(FlaskGetCodeByOrder, "/get_code_by_order")
api.add_resource(FlaskPOOrderCode, "/po/get-code")
api.add_resource(FlaskTriggerRetryGetCode, "/retry-code")
api.add_resource(FlaskCheckBalance, "/check-balance/<string:vendor_name>")

# public api for webhook
bp_public = Blueprint("api_pb", __name__, url_prefix="/v1/ura12")

api_pb = Api(bp_public)
api_pb.add_resource(FlaskGetCodeWebhook, "/webhook")
api_pb.add_resource(FlaskVietguysWebhook, "/webhook/vgs")
api_pb.add_resource(FlaskGrabWebhook, "/webhook/grab")
