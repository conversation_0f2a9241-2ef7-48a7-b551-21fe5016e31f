#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from loguru import logger

from flask import Flask
from flask_cors import CORS
from werkzeug.exceptions import default_exceptions

from app.errors.handler import api_error_handler
from app.extensions import db, apm


def create_app(config_app):
    app = Flask(__name__)
    CORS(app)
    app.config.from_object(config_app)
    __init_app(app)
    __config_logging(app)
    __register_blueprint(app)
    __config_error_handlers(app)
    __config_apm(app)

    return app


def __config_logging(app):
    logger.info("Start flask...🚀🚀🚀")


def __register_blueprint(app):
    from flask_app.api import bp as api_bp
    from flask_app.api import bp_public as api_bp_public

    app.register_blueprint(api_bp)
    app.register_blueprint(api_bp_public)


def __init_app(app):
    db.init_app(app)


def __config_error_handlers(app):
    for exp in default_exceptions:
        app.register_error_handler(exp, api_error_handler)
    app.register_error_handler(Exception, api_error_handler)


def __config_apm(app):
    apm.init_app(
        app,
        server_url=os.environ.get("ELASTIC_APM_SERVER_URL"),
        service_name="ura12",
        secret_token=os.environ.get("ELASTIC_APM_SECRET_TOKEN"),
        environment=os.environ.get("ELASTIC_APM_ENVIRONMENT"),
        debug=True,
    )
