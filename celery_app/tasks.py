from celery import shared_task

from celery_app.workers.send_telegram_message_task import SendTelegramMessageTask
# from celery_app.workers.pop_codex_to_storage_task import PopCodexToStorageTask
from celery_app.workers.supplier_order_detail_import_code_urbox_task import (
    SupplierOrderDetailImportCodeUrboxTask,
)


# @shared_task(bind=True, base=SupplierOrderDetailImportCodeUrboxTask)
# def SupplierOrderDetailImportCodeUrboxTask(self):
#     self.run_task()

#
# @shared_task(bind=True, base=PopCodexToStorageTask)
# def PopCodexToStorageTask(self):
#     self.run_task()

@shared_task(bind=True, base=SendTelegramMessageTask)
def SendTelegramMessageTask(self):
    self.run_task()
