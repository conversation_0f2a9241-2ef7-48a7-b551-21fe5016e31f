#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time

from celery import Celery, Task
from loguru import logger

from app.const import (
    REDIS_KEY_FLASK_CELERY_TASK,
    FLASK_CELERY_TASK_SLEEP,
    <PERSON><PERSON><PERSON>_TIME_RUNNING_TASK,
)
from app.extensions import redis_client
from app.helper import Helper
from celery_app.config import (
    REDIS_USERNAME,
    REDIS_PASSWORD,
    REDIS_HOST,
    REDIS_PORT,
    REDIS_DB,
    DAY,
    BEAT_SCHEDULE,
    TASK_REDIS_KEY,
    TASK_SLEEP,
)


def create_app():
    app = Celery(
        __name__,
        backend="redis://%s:%s@%s:%s/%s"
        % (REDIS_USERNAME, REDIS_PASSWORD, REDIS_HOST, REDIS_PORT, REDIS_DB),
        broker="redis://%s:%s@%s:%s/%s"
        % (R<PERSON><PERSON>_USERNAME, REDIS_PASSWORD, RED<PERSON>_HOST, REDIS_PORT, REDIS_DB),
    )

    app.conf.update(
        task_track_started=True,
        task_serializer="json",
        accept_content=["json"],  # Ignore other content
        result_serializer="json",
        timezone="Asia/Bangkok",
        enable_utc=True,
        imports=("celery_app.tasks",),
        result_expires=DAY,
        beat_schedule=BEAT_SCHEDULE,
    )
    return app


class FlaskCeleryTask(Task):
    def __init__(self, *args, **kwargs):
        self.flag_name = None
        self.ignore_result = False

    def run_task(self):
        if self.flag_name is None:
            raise Exception("`flag_name` is not defined")

        if self.is_running():
            self.open_task_flag(int(time.time()))
            try:
                self.start_task()
            except Exception as e:
                logger.exception(e)
            self.close_task_flag()

    def is_running(self):
        flag = self.get_task_flag()
        if not flag or int(flag) == 0:
            return True
        return False

    def get_task_flag(self):
        ping = redis_client.ping()
        if ping is True:
            flag = redis_client.hget(REDIS_KEY_FLASK_CELERY_TASK, self.flag_name)
        else:
            flag = 1
        return flag

    def set_task_flag(self, value):
        ping = redis_client.ping()
        if ping is True:
            redis_client.hset(REDIS_KEY_FLASK_CELERY_TASK, self.flag_name, value)

    def open_task_flag(self, value):
        self.set_task_flag(int(time.time()))

    def close_task_flag(self):
        self.set_task_flag(0)

    def task_sleep(self):
        time.sleep(FLASK_CELERY_TASK_SLEEP)

    def start_task(self):
        pass

    def is_health(self):
        flag = self.get_task_flag()
        if int(flag) == 1:
            return False
        return True

    def is_time_break(self, start_time):
        end_time = Helper.get_now_unix_timestamp()
        delta_time = end_time - start_time
        if delta_time <= LIMIT_TIME_RUNNING_TASK:
            return False
        return True
