#!/usr/bin/env python
# -*- coding: utf-8 -*-
from app.const import QUANTITY_CONFIG_KEY, LIMIT_NUMBER_OF_CODEX
from app.libs.base.utility import Utility
from app.libs.apis.a6api import A6Api
from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
from app.repositories import codex_repo
from celery_app import FlaskCeleryTask


class PushCodexToStorageTask(FlaskCeleryTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.flag_name = "TASK_PUSH_CODEX_TO_STORAGE"

    def start_task(self):
        products = A6Api.get_product_need_get_code()
        if products is None:
            return

        for product in products:
            self.get_product_code(product)
        return

    def get_product_code(self, product):
        quantity = self.get_quantity_needed(
            product.get("code_min"), product.get("code_stock")
        )
        for supplier in product["product_suppliers"]:
            if quantity < 1:
                return
            behavior = ContextCodeApi.get_behavior_by_supplier(supplier)
            if behavior is None:
                continue
            context = ContextCodeApi(behavior)
            codes = context.get_code(
                supplier["id"],
                supplier["supplier_id"],
                supplier["product_id"],
                supplier["product_code"],
                quantity,
                supplier["supplier_value"],
            )
            quantity = quantity - len(codes)
            self.save_code_to_database(
                product["id"], supplier["supplier_id"], supplier["whoexport"], codes
            )
            self.update_quantity_stock(product["id"], len(codes))

    def get_quantity_needed(self, quantity_constant, quantity_stock):
        quantity_constant = 0 if quantity_constant is None else quantity_constant
        quantity_stock = 0 if quantity_stock is None else quantity_stock
        quantity_config = Utility.get_config(QUANTITY_CONFIG_KEY)

        quantity_needed = quantity_constant - quantity_stock + quantity_config
        if quantity_needed > LIMIT_NUMBER_OF_CODEX:
            quantity_needed = LIMIT_NUMBER_OF_CODEX
        elif quantity_needed < 0:
            quantity_needed = 0
        return quantity_needed

    def save_code_to_database(self, product_id, supplier_id, code_type, codes):
        for code in codes:
            codex_repo.create(
                product_id=product_id,
                supplier_id=supplier_id,
                codex=code["codex"],
                serial=code["serial"],
                pin=code["pin"],
                expired_time=code["expired"],
                code_type=code_type,
            )

    def update_quantity_stock(self, product_id, quantity):
        A6Api.update_quantity_stock_of_code(product_id=product_id, quantity=quantity)
