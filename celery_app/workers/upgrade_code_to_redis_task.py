#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
from datetime import date

from loguru import logger

from app.const import (
    REDIS_KEY_STORE_CODE,
    CODE_AVAILABLE_DELTA_DAYS,
    STATUS_ON,
    STATUS_OFF,
)
from app.extensions import redis_client
from app.helper import Helper
from app.libs.base.utility import Utility
from app.repositories import codex_repo
from celery_app import FlaskCeleryTask


class UpgradeCodeToRedisTask(FlaskCeleryTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.flag_name = "UPGRADE_CODE_TO_REDIS_TASK"

    def start_task(self):
        start_time = Helper.get_now_unix_timestamp()
        while True:
            if not self.is_health():
                return

            if self.is_time_break(start_time):
                return

            self.upgrade_codes()
            Utility.sleep()

    def upgrade_codes(self):
        codes = codex_repo.get_code_outside_redis()
        for code in codes:
            self.transcoding_to_redis(code)

    def transcoding_to_redis(self, code):
        if not redis_client.ping():
            return

        try:
            if self.is_validate_code_to_redis(code):
                is_redis = self.push_code_redis(code)
                if is_redis:
                    code.update_code_to_redis()
        except Exception as e:
            logger.exception(e)

    def push_code_redis(self, code):
        try:
            if not redis_client.ping():
                return False

            redis_client.lpush(
                "{prefix}{product_id}".format(
                    prefix=REDIS_KEY_STORE_CODE, product_id=code.product_id
                ),
                json.dumps(
                    dict(
                        id=code.id,
                        code=code.codex,
                        serial=code.serial,
                        pin=code.pin,
                        expired=code.expired_time,
                    )
                ),
            )
        except Exception as e:
            logger.exception(e)
            return False
        return True

    def is_validate_code_to_redis(self, code):
        return (
            (
                code.expired_time == 0
                or code.expired_time
                > Utility.add_days(date.today(), CODE_AVAILABLE_DELTA_DAYS)
            )
            and code.status == STATUS_ON
            and code.is_redis == STATUS_OFF
            and code.cart_id == 0
            and code.cart_detail_id == 0
        )
