#!/usr/bin/env python
# -*- coding: utf-8 -*-
from loguru import logger

from app.config import config_app
from app.const import (
    STATUS_ON,
    EXCEL_FORMAT_IMPORT_CODE_URBOX,
    EXCEL_IMPORT_CODE_URBOX_CODEX,
    EXCEL_IMPORT_CODE_URBOX_EXPIRED,
    EXCEL_IMPORT_CODE_URBOX_SERIAL,
    EXCEL_IMPORT_CODE_URBOX_PIN,
)
from app.extensions import kafka_producer
from app.libs.base.redis_utils import RedisUtility
from app.libs.apis.a18api import A18Api
from app.libs.apis.a21api import A21Api
from app.libs.apis.a6api import A6Api
from app.libs.apis.a8api import A8Api
from app.libs.base.utility import Utility
from app.repositories import codex_repo
from app.security import UrboxTripleDes
from celery_app import FlaskCeleryTask
from app.libs.apis.third_party_api.abstract_api import AbstractApi

class SupplierOrderDetailImportCodeUrboxTask(FlaskCeleryTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.flag_name = "SUPPLIER_ORDER_DETAIL_IMPORT_CODE_URBOX_TASK"

    def start_task(self):
        logger.info("Hello")

        if RedisUtility.get_kafka_flag() == STATUS_ON:
            logger.info("Flag all off")
            files = A18Api.get_supplier_order_file_name()
            if files is None:
                return {"msg": "empty supplier order"}

            for file in files:
                if not self.is_health():
                    return

                respose = self.handle_file(file)

                if (
                    respose.get("quantity_success") is None
                    or respose.get("quantity_success") == ""
                ):
                    return logger.error(respose)
                if (
                    respose.get("quantity_error") is None
                    or respose.get("quantity_error") == ""
                ):
                    return logger.error(respose)

                A21Api.update_order_log(
                    id=file.get("id"),
                    quantity_success=int(respose.get("quantity_success")),
                    quantity_error=int(respose.get("quantity_error")),
                    process=3,
                )

                Utility.sleep()
        else:
            logger.info("Flag all on")
            return {"flag": " not none"}

    def handle_file(self, file):
        logger.error(file)
        page = 1
        quantity_success = file.get("quantity_success")
        quantity_error = file.get("quantity_error")
        product_id = file.get("product_id")

        list_product_ids = [214, 215, 216, 218, 472, 4047]
        # list_product_ids = []

        if product_id is None or product_id == "":
            return {"msg": "product_id is required"}

        if product_id not in list_product_ids:
            return {"msg": "product_id is not access"}

        response_processing = A21Api.update_process_supplier_order(
            id=file.get("id"), process=2
        )

        if response_processing.get("success") is not True:
            return {"msg": "a21 is not success"}
        supplier_order_id = file.get("id")
        supplier_id = file.get("supplier_id")

        if product_id is None or product_id == "":
            return {"msg": "product_id is required"}

        product_codex_response = A6Api.get_product_codex_by_id(
            product_id=file.get("product_id")
        )

        product_codex = str(product_codex_response.get("items")[0].get("codex"))
        product_valuex = int(product_codex_response.get("items")[0].get("valuex"))

        if (
            product_codex_response.get("items")[0].get("product_parent_id") is None
            or product_codex_response.get("items")[0].get("product_parent_id") == ""
        ):
            return {"msg": "product_parent_id is required"}
        if product_codex is None or product_codex == "":
            return {"msg": "product_parent_id is product_codex"}

        product_parent_id = int(
            product_codex_response.get("items")[0].get("product_parent_id")
        )

        while True:
            data_from_file = self.get_data_from_file_a8(file, page)

            if data_from_file is None:
                return dict(
                    quantity_error=quantity_error,
                    quantity_success=quantity_success,
                    message=None,
                )
            data = self.parse_data(data_from_file, page)
            quantity = self.save(
                data,
                supplier_order_id,
                product_codex,
                product_id,
                quantity_success,
                quantity_error,
                product_parent_id,
                supplier_id,
                product_valuex,
            )

            quantity_success = quantity.get("quantity_success")
            quantity_error = quantity.get("quantity_error")
            page += 1
            Utility.sleep()

    def get_data_from_file_a8(self, supplier_order, page=1):
        data = []

        try:
            params = dict(name=supplier_order.get("filename"), page=page)
            response = A8Api.get_data_from_file(params, page)
            return response
        except Exception as e:
            return None

    def get_items(self, data_from_file, page):
        if page == 1:
            header = data_from_file[0]
            items = data_from_file[1:]
            for header_index in range(0, len(EXCEL_FORMAT_IMPORT_CODE_URBOX)):
                try:
                    if (
                        header[header_index]
                        != EXCEL_FORMAT_IMPORT_CODE_URBOX[header_index]
                    ):
                        return []
                except Exception as e:
                    return []
        else:
            items = data_from_file
        return items

    def parse_data(self, data_from_file, page):
        data = []

        items = self.get_items(data_from_file, page)

        for item in items:
            try:
                if (
                    item[EXCEL_IMPORT_CODE_URBOX_CODEX] is not None
                    and item[EXCEL_IMPORT_CODE_URBOX_EXPIRED] is not None
                ):
                    data.append(
                        AbstractApi().format_code(
                            codex=str(item[EXCEL_IMPORT_CODE_URBOX_CODEX]),
                            expired=int(item[EXCEL_IMPORT_CODE_URBOX_EXPIRED])
                            + 61199,
                            serial=str(item[EXCEL_IMPORT_CODE_URBOX_SERIAL]),
                            pin=str(item[EXCEL_IMPORT_CODE_URBOX_PIN]),
                        )
                    )
            except Exception as e:
                logger.exception(e)
        # data = [dict(item) for item in set(frozenset(tmp.items()) for tmp in data)]
        return data

    def save(
        self,
        codes_in_file,
        supplier_order_id,
        product_codex,
        product_id,
        quantity_success,
        quantity_error,
        product_parent_id,
        supplier_id,
        product_valuex,
    ):
        logger.error(codes_in_file)
        codexs_in_file = [code_in_file.get("codex") for code_in_file in codes_in_file]

        for i, codexs in enumerate(codexs_in_file):
            codexs_in_file[i] = codexs
        codes_exists_in_db = codex_repo.get_by_codex_and_product_parent_id(
            codexs_in_file, product_parent_id
        )

        codexs_exists_in_db = [
            code_exists_in_db.codex for code_exists_in_db in codes_exists_in_db
        ]
        logger.error(codes_in_file)
        if codes_in_file:
            # logger.error(supplier_order_id)

            # for not_empty_code in codes_in_file:
            #     if not_empty_code.get('codex') is not None and not_empty_code.get('codex') != '':
            #         not_empty_code_in_file = not_empty_code
            for code_in_file in codes_in_file:
                if (
                    code_in_file.get("codex") is not None
                    and code_in_file.get("codex") != ""
                ):
                    # logger.error(18)
                    # logger.error(code_in_file)
                    if (
                        code_in_file.get("codex")
                        not in codexs_exists_in_db
                    ):
                        codex_create = codex_repo.create(
                            codex=code_in_file.get("codex"),
                            pin=code_in_file.get("pin"),
                            expired_time=code_in_file.get("expired"),
                            serial=code_in_file.get("serial"),
                            supplier_id=supplier_id,
                            product_id=product_id,
                            product_parent_id=product_parent_id,
                            codex_int=code_in_file.get("codex_int"),
                        )
                        A21Api.create_supplier_order_detail(
                            codex=code_in_file.get("codex"),
                            serial=code_in_file.get("serial"),
                            pin=code_in_file.get("pin"),
                            expired_time=code_in_file.get("expired"),
                            process=2,
                            supplier_order_id=supplier_order_id,
                            note="",
                            product_code=product_codex,
                            money=product_valuex,
                        )
                        quantity_success += 1

                        RedisUtility.push_code_to_redis(codex_create)
                        self.update_quantity_stock(product_id, 1)
                        codexs_exists_in_db.append(
                            UrboxTripleDes.encode(code_in_file.get("codex"))
                        )
                    else:
                        response = A21Api.create_supplier_order_detail(
                            codex=code_in_file.get("codex"),
                            serial=code_in_file.get("serial"),
                            pin=code_in_file.get("pin"),
                            expired_time=code_in_file.get("expired"),
                            process=3,
                            supplier_order_id=supplier_order_id,
                            note="code bị trùng",
                            product_code=product_codex,
                            money=product_valuex,
                        )
                        quantity_error += 1

                Utility.sleep()
        return dict(quantity_error=quantity_error, quantity_success=quantity_success)

    def update_quantity_stock(self, product_id, quantity):
        kafka_producer.push(
            config_app.TOPIC_CHANGE_QUANTITY,
            {
                "type": "ADDITION",
                "payload": [{"quantity": quantity, "product_id": product_id}],
            },
        )

    # def push_code_to_redis(self, codex):
    #     try:
    #         if not Redis.ping():
    #             return False
    #
    #         key = "{prefix}{product_id}".format(
    #             prefix = REDIS_KEY_STORE_CODE,
    #             product_id = codex.product_id
    #         )
    #         value = dict(
    #             id = codex.id,
    #             code = codex.codex,
    #             serial = codex.serial,
    #             pin = codex.pin,
    #             expired = codex.expired_time
    #         )
    #         Redis.lpush(key, json.dumps(value))
    #         codex.update_code_to_redis()
    #     except Exception as e:
    #         logger.exception(e)
