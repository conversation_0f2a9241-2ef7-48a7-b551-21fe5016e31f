#!/usr/bin/env python
# -*- coding: utf-8 -*-
from datetime import datetime, date
import json
import datetime
import logging

from app.const import REDIS_KEY_STORE_CODE, CODE_AVAILABLE_DELTA_DAYS
from app.extensions import redis_client
from app.helper import Helper
from app.libs.base.utility import Utility
from app.repositories import codex_repo
from celery_app import FlaskCeleryTask

logger = logging.getLogger(__name__)


class PopCodexToStorageTask(FlaskCeleryTask):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.flag_name = "POP_CODEX_TO_STORAGE_TASK"

    def start_task(self):
        start_time = Helper.get_now_unix_timestamp()
        while True:
            if not self.is_health():
                return

            if self.is_time_break(start_time):
                return

            self.pop_code()
            Utility.sleep()

    def pop_code(self):
        ping = redis_client.ping()
        if ping is not True:
            return

        store_keys = redis_client.keys(REDIS_KEY_STORE_CODE + "*")

        if len(store_keys) == 0:
            return

        for redis_key in store_keys:
            self.pop_code_process(redis_key)

    def pop_code_process(self, redis_key):
        try:
            redis_items = redis_client.lrange(redis_key, 0, -1)

            for redis_item in redis_items:
                code = json.loads(redis_item)
                expired = code["expired"] or 0
                if (
                    "expired" in code
                    and Utility.add_days(
                        date.today(), CODE_AVAILABLE_DELTA_DAYS
                    )
                    > expired
                    > 0
                ):
                    redis_client.lrem(redis_key, 1, redis_item)
                    codex_repo.update_code_to_storage(code)
        except Exception as e:
            logger.exception(e)
