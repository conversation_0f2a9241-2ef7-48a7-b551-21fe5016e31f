#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os

from celery.schedules import crontab

REDIS_HOST = os.environ.get("REDIS_HOST") or "0.0.0.0"
REDIS_PORT = os.environ.get("REDIS_PORT") or "27017"
REDIS_USERNAME = os.environ.get("REDIS_USERNAME") or ""
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD") or ""
REDIS_DB = os.environ.get("REDIS_DB") or 0

TASK_SLEEP = 0.01
TASK_REDIS_KEY = "A12:CELERY_TASK:FLAG"
REDIS_KEY_STORE_CODE = "A12:STORES:CODE:"
CODE_AVAILABLE_DELTA_DAYS = 7

DAY = 24 * 60 * 60

BEAT_SCHEDULE = {
    # "SupplierOrderDetailImportCodeUrboxTask": {
    #     "task": "celery_app.tasks.SupplierOrderDetailImportCodeUrboxTask",
    #     "schedule": crontab(minute="*/2"),
    #     "args": {},
    # },
    # "PopCodexToStorageTask": {
    #     "task": "celery_app.tasks.PopCodexToStorageTask",
    #     "schedule": crontab(minute="*/60"),
    #     "args": {},
    # },
    "SendTelegramMessageTask": {
        "task": "celery_app.tasks.SendTelegramMessageTask",
        'schedule': 60,
        "args": {},
    },
}
