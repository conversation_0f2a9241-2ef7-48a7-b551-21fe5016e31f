# Stage 1 - Install build dependencies
FROM 848535873250.dkr.ecr.ap-southeast-1.amazonaws.com/python:3.9-slim AS builder

RUN apt-get update \
    && apt-get install git -y \
    && apt-get install -y --reinstall ca-certificates \
    && pip install pipenv
RUN pip install --upgrade pip
COPY requirements.txt .
RUN pip install --prefix=/install --ignore-installed -r requirements.txt


# Stage 2 - Copy only necessary files to the runner stage
FROM python:3.9-slim

COPY --from=builder /install /usr/local

WORKDIR /home/<USER>/code

COPY . .
COPY supervisord.conf /etc/supervisor/supervisord.conf

ENV PYTHONUNBUFFERED=1
ENV TZ=Asia/Bangkok
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN sed -i -e 's/\r$//' entry-point.sh
RUN chmod +x entry-point.sh


EXPOSE 8000

ENTRYPOINT ["./entry-point.sh"]
