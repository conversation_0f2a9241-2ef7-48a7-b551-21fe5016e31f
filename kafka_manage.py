#!/usr/bin/env python
# -*- coding: utf-8 -*-
from loguru import logger

from app.config import config_app, ConfigAppota
from kafka_app import KafkaFactory, KafkaType

from kafka_app.consumer_worker.consumer_generate_code import ConsumerGenerateCode
from kafka_app.consumer_worker.consumer_insert_codex_data import ConsumerInsertCodexData
from kafka_app.consumer_worker.consumer_po_order_code import ConsumerPOOrderCode
from kafka_app.consumer_worker.consumer_before_po_order_code import ConsumerBeforePOOrderCode

# Validate Appota configuration on startup
try:
    ConfigAppota.validate_config()
except Exception as e:
    logger.error(f"Configuration validation failed: {e}")
    exit(1)

configs = config_app
configs.FUNCTION_CONSUMER_SUBSCRIBE = {
    f"[{config_app.TOPIC_GENERATE_CODE}][GENERATE_CODE]": ConsumerGenerateCode.running,
    f"[{config_app.TOPIC_PO_ORDER_CODE}][PO_ORDER_CODE]": ConsumerPOOrderCode.running,
    f"[{config_app.TOPIC_BEFORE_PO_ORDER_CODE}][BEFORE_PO_ORDER_CODE]": ConsumerBeforePOOrderCode.running,
    f"[{config_app.TOPIC_INSERT_CODEX_DATA}][INSERT_CODEX_DATA]": ConsumerInsertCodexData.running,
}

logger.info("STARTED KAFKA WORKER...🚝 🚝 🚝")

kafka_consumer = KafkaFactory.create(
    KafkaType.CONSUMER, config_app.KAFKA_URL, config_app
)
kafka_consumer.start(config_app.KAFKA_GROUP)

logger.info("RUNNING...🏇 🏇 🏇")
