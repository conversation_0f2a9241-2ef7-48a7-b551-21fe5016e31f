#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Default values
TEST_PATH=""
VERBOSE="-v"

# Help function
function show_help {
    echo -e "${YELLOW}Docker Compose Test Runner${NC}"
    echo "Usage: ./run_tests_docker.sh [options] [test_path]"
    echo ""
    echo "Options:"
    echo "  -h, --help       Show this help message"
    echo "  -v, --verbose    Run tests in verbose mode (default)"
    echo "  -q, --quiet      Run tests in quiet mode"
    echo "  -x, --exitfirst  Exit instantly on first error"
    echo ""
    echo "Examples:"
    echo "  ./run_tests_docker.sh                                  # Run all tests"
    echo "  ./run_tests_docker.sh src/app/tests/                   # Run all tests in the tests directory"
    echo "  ./run_tests_docker.sh -x src/app/tests/libs/           # Run tests in libs directory and exit on first error"
    echo "  ./run_tests_docker.sh src/app/tests/libs/apis/third_party_api/test_behavior_grab_api_params.py  # Run specific test file"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE="-v"
            shift
            ;;
        -q|--quiet)
            VERBOSE=""
            shift
            ;;
        -x|--exitfirst)
            VERBOSE="$VERBOSE -x"
            shift
            ;;
        *)
            TEST_PATH="$1"
            shift
            ;;
    esac
done

# Check if Docker Compose is running
if ! docker compose ps | grep -q "Up"; then
    echo -e "${RED}Error: Docker Compose services are not running.${NC}"
    echo -e "Please start the services with: ${YELLOW}docker compose up -d${NC}"
    exit 1
fi

# Run the tests
echo -e "${YELLOW}Running tests with Docker Compose...${NC}"

if [ -z "$TEST_PATH" ]; then
    echo -e "${YELLOW}Running all tests...${NC}"
    docker compose exec ura12-api python -m pytest $VERBOSE
else
    echo -e "${YELLOW}Running tests in: $TEST_PATH${NC}"
    docker compose exec ura12-api python -m pytest $VERBOSE $TEST_PATH
fi

# Check the exit code
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}All tests passed!${NC}"
else
    echo -e "${RED}Some tests failed. Please check the output above.${NC}"
fi

exit $EXIT_CODE
